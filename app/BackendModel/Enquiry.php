<?php

namespace App\BackendModel;

use App\ActivityLogger\Traits\HasActivityLogs;
use App\Agency;
use App\AutomationRule;
use App\CallMaster;
use App\Common\Common;
use App\Common\Notifications;
use App\Common\Unguarded;
use App\Common\Variables;
use App\Constants\MarbleGallery;
use App\CustomFieldValue;
use App\Deal;
use App\Events\ApiHistoryPost;
use App\Events\CreateFollowup;
use App\Events\LeadAdded;
use App\Events\SendPusherNotification;
use App\Facades\AutomationFacade;
use App\FrontendModel\AssignAgent;
use App\FrontendModel\LeadAdditionalDetails;
use App\FrontendModel\LeadAdditionalField;
use App\Jobs\SendNotification;
use App\Mail\NewLeadAdded;
use App\PusherSetting;
use App\Task;
use App\User;
use Barryvdh\LaravelIdeHelper\Eloquent;
use Carbon\Carbon;
use Getlead\Campaign\Models\LeadCampaign;
use Getlead\Messagebird\Common\GupShup;
use Getlead\Messagebird\Models\WatsappCredential;
use Getlead\Sales\Models\EnquirySalesField;
use Getlead\Sales\Models\Order;
use Getlead\Sales\Models\PaymentCollection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Laravel\Scout\Searchable;
use Swift_Mailer;
use Swift_SmtpTransport;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 *
 *
 * @property int $pk_int_enquiry_id
 * @property int|null $fk_int_user_id
 * @property int|null $fk_int_purpose_id
 * @property int|null $district_id
 * @property int|null $taluk_id
 * @property int|null $payment_reffer_id
 * @property string|null $vchr_customer_name
 * @property string|null $date_of_birth
 * @property string|null $vchr_customer_company_name
 * @property int|null $country_code
 * @property string|null $mobile_no
 * @property int|null $lead_type_id
 * @property string|null $purchase_date
 * @property string|null $function_date
 * @property string|null $exp_wt_grams expected_wt_in_grams
 * @property int|null $designation_id
 * @property string|null $vchr_customer_mobile
 * @property string|null $landline_number
 * @property string|null $more_phone_numbers
 * @property string|null $vchr_customer_email
 * @property string|null $vchr_enquiry_feedback
 * @property int $fk_int_enquiry_type_id
 * @property int $int_status
 * @property int $lead_attension
 * @property string|null $address
 * @property string|null $fb_ad_id
 * @property string|null $fb_ad_info
 * @property string|null $read_status
 * @property int $new_status
 * @property int|null $feedback_status
 * @property int $followup_required
 * @property int|null $staff_id
 * @property string|null $next_follow_up
 * @property string|null $model_id
 * @property string|null $purchase_plan
 * @property string|null $date_of_purchase
 * @property string|null $live_deal
 * @property string|null $remarks
 * @property string|null $competing_model
 * @property string|null $assigned_date
 * @property string|null $read_at
 * @property string|null $photo
 * @property string|null $event_date
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property int|null $deleted_by
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $otp
 * @property string|null $otp_validity
 * @property string|null $password
 * @property int $otp_attempts
 * @property string|null $otp_attempt_date
 * @property int|null $reason_id
 * @property int|null $woocommerce_id
 * @property string|null $latitude
 * @property string|null $longitude
 * @property string|null $customer_code
 * @property int $email_sync
 * @property int|null $agency_id
 * @property-read \Illuminate\Database\Eloquent\Collection<int, LeadAdditionalDetails> $additionalDetails
 * @property-read int|null $additional_details_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, LeadAdditionalDetails> $additional_details
 * @property-read User|null $assigned_user
 * @property-read \Illuminate\Database\Eloquent\Collection<int, CallMaster> $callMaster
 * @property-read int|null $call_master_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Getlead\Campaign\Models\CampaignLead> $campaignLead
 * @property-read int|null $campaign_lead_count
 * @property-read User|null $createdBy
 * @property-read \Illuminate\Database\Eloquent\Collection<int, CustomFieldValue> $custom_field_values
 * @property-read int|null $custom_field_values_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Deal> $deal
 * @property-read int|null $deal_count
 * @property-read \App\BackendModel\District|null $district
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\BackendModel\EnquiryFollowup> $enquiryFollowup
 * @property-read int|null $enquiry_followup_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\BackendModel\EnquiryFollowup> $enquiryFollowupBySystem
 * @property-read int|null $enquiry_followup_by_system_count
 * @property-read EnquirySalesField|null $enquiry_sales
 * @property-read \App\BackendModel\FeedbackStatus|null $feedbackStatus
 * @property-read mixed $did_call_number
 * @property-read HasOne $district_name
 * @property-read mixed $name_with_number
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Ivr\Models\Ivr> $ivrCall
 * @property-read int|null $ivr_call_count
 * @property-read Task|null $lastCallTask
 * @property-read Task|null $lastTask
 * @property-read \App\BackendModel\EnquiryFollowup|null $last_followup
 * @property-read Task|null $last_task
 * @property-read \App\BackendModel\EnquiryPurpose|null $leadPurpose
 * @property-read \App\BackendModel\EnquiryType|null $leadSource
 * @property-read \Illuminate\Database\Eloquent\Collection<int, PaymentCollection> $payments
 * @property-read int|null $payments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Order> $sales
 * @property-read int|null $sales_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Task> $task
 * @property-read int|null $task_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\VisitUser> $visit
 * @property-read int|null $visit_count
 * @method static Builder|Enquiry newModelQuery()
 * @method static Builder|Enquiry newQuery()
 * @method static Builder|Enquiry onlyTrashed()
 * @method static Builder|Enquiry query()
 * @method static Builder|Enquiry searchLegacy($keyword)
 * @method static Builder|Enquiry whereAddress($value)
 * @method static Builder|Enquiry whereAgencyId($value)
 * @method static Builder|Enquiry whereAssignedDate($value)
 * @method static Builder|Enquiry whereCompetingModel($value)
 * @method static Builder|Enquiry whereCountryCode($value)
 * @method static Builder|Enquiry whereCreatedAt($value)
 * @method static Builder|Enquiry whereCreatedBy($value)
 * @method static Builder|Enquiry whereCustomerCode($value)
 * @method static Builder|Enquiry whereDateOfBirth($value)
 * @method static Builder|Enquiry whereDateOfPurchase($value)
 * @method static Builder|Enquiry whereDeletedAt($value)
 * @method static Builder|Enquiry whereDeletedBy($value)
 * @method static Builder|Enquiry whereDesignationId($value)
 * @method static Builder|Enquiry whereDistrictId($value)
 * @method static Builder|Enquiry whereEmailSync($value)
 * @method static Builder|Enquiry whereEventDate($value)
 * @method static Builder|Enquiry whereExpWtGrams($value)
 * @method static Builder|Enquiry whereFbAdId($value)
 * @method static Builder|Enquiry whereFbAdInfo($value)
 * @method static Builder|Enquiry whereFeedbackStatus($value)
 * @method static Builder|Enquiry whereFkIntEnquiryTypeId($value)
 * @method static Builder|Enquiry whereFkIntPurposeId($value)
 * @method static Builder|Enquiry whereFkIntUserId($value)
 * @method static Builder|Enquiry whereFollowupRequired($value)
 * @method static Builder|Enquiry whereFunctionDate($value)
 * @method static Builder|Enquiry whereIntStatus($value)
 * @method static Builder|Enquiry whereLandlineNumber($value)
 * @method static Builder|Enquiry whereLatitude($value)
 * @method static Builder|Enquiry whereLeadAttension($value)
 * @method static Builder|Enquiry whereLeadTypeId($value)
 * @method static Builder|Enquiry whereLiveDeal($value)
 * @method static Builder|Enquiry whereLongitude($value)
 * @method static Builder|Enquiry whereMobileNo($value)
 * @method static Builder|Enquiry whereModelId($value)
 * @method static Builder|Enquiry whereMorePhoneNumbers($value)
 * @method static Builder|Enquiry whereNewStatus($value)
 * @method static Builder|Enquiry whereNextFollowUp($value)
 * @method static Builder|Enquiry whereOtp($value)
 * @method static Builder|Enquiry whereOtpAttemptDate($value)
 * @method static Builder|Enquiry whereOtpAttempts($value)
 * @method static Builder|Enquiry whereOtpValidity($value)
 * @method static Builder|Enquiry wherePassword($value)
 * @method static Builder|Enquiry wherePaymentRefferId($value)
 * @method static Builder|Enquiry wherePhoto($value)
 * @method static Builder|Enquiry wherePkIntEnquiryId($value)
 * @method static Builder|Enquiry wherePurchaseDate($value)
 * @method static Builder|Enquiry wherePurchasePlan($value)
 * @method static Builder|Enquiry whereReadAt($value)
 * @method static Builder|Enquiry whereReadStatus($value)
 * @method static Builder|Enquiry whereReasonId($value)
 * @method static Builder|Enquiry whereRemarks($value)
 * @method static Builder|Enquiry whereStaffId($value)
 * @method static Builder|Enquiry whereTalukId($value)
 * @method static Builder|Enquiry whereUpdatedAt($value)
 * @method static Builder|Enquiry whereUpdatedBy($value)
 * @method static Builder|Enquiry whereVchrCustomerCompanyName($value)
 * @method static Builder|Enquiry whereVchrCustomerEmail($value)
 * @method static Builder|Enquiry whereVchrCustomerMobile($value)
 * @method static Builder|Enquiry whereVchrCustomerName($value)
 * @method static Builder|Enquiry whereVchrEnquiryFeedback($value)
 * @method static Builder|Enquiry whereWoocommerceId($value)
 * @method static Builder|Enquiry withTrashed()
 * @method static Builder|Enquiry withoutTrashed()
 * @mixin \Eloquent
 */
class Enquiry extends Authenticatable implements JWTSubject
{
    use SoftDeletes;
    use Unguarded;
    use Searchable;
    use HasActivityLogs;

    const SHOW = 0;
    const NOTSHOW = 1;

    public function searchableAs(): string
    {
        return 'enquiries_index';
    }

    /**
     * @return array<string,mixed>
     */
    public function toSearchableArray(): array
    {
        $morePhones = is_string($this->more_phone_numbers)
            ? json_decode($this->more_phone_numbers, true)
            : $this->more_phone_numbers;

        $morePhones = is_array($morePhones) ? array_values($morePhones) : [];

        return [
            'pk_int_enquiry_id' => $this->pk_int_enquiry_id,
            'fk_int_user_id' => $this->fk_int_user_id,
            'vchr_customer_name' => $this->vchr_customer_name,
            'mobile' => $this->mobile_no,
            'vchr_customer_mobile' => $this->vchr_customer_mobile,
            'vchr_customer_email' => $this->vchr_customer_email,
            'vchr_customer_company_name' => $this->vchr_customer_company_name,
            'all_phone_numbers' => array_filter([
                $this->vchr_customer_mobile,
                $this->mobile_no,
                ...$morePhones,
                substr($this->vchr_customer_mobile, -4)
            ])
        ];
    }


    public function searchIndexShouldBeUpdated(): bool
    {
        return $this->wasRecentlyCreated || $this->isDirty([
                'vchr_customer_name',
                'mobile_no',
                'vchr_customer_mobile',
                'vchr_customer_email',
                'vchr_customer_company_name',
                'more_phone_numbers'
            ]);
    }

    protected $dates = ['deleted_at'];
    protected $primaryKey = 'pk_int_enquiry_id';
    protected $table = 'tbl_enquiries';

    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s', // Adjust the format according to your needs
        'updated_at' => 'datetime:Y-m-d H:i:s', // Adjust the format according to your needs
        //  'more_phone_numbers' => 'array',
    ];

    public static $rulesMessage = [
        'fk_int_enquiry_type_id.required' => 'Enquiry Source is required.',
        'vchr_customer_mobile.required' => 'Mobile is required.',
        'vchr_customer_mobile.digits_between' => 'Mobile number must be between 6 to 14 digits',
        'vchr_customer_mobile.unique' => 'Lead already exist with same mobile number.',
    ];


    public static $rulesUpdate = [
        'vchr_customer_mobile' => 'required'
    ];

    public static $rulesMessageUpdate = [
        'vchr_customer_mobile.required' => 'Mobile is required.',
    ];

    public const DISPLAY_FIELDS = [
        [
            "id" => 1,
            "caption" => "Name",
            "code" => "name"
        ],
        [
            "id" => 2,
            "caption" => "Phone",
            "code" => "phone"
        ],
        [
            "id" => 3,
            "caption" => "Assigned To",
            "code" => "assigned_to"
        ],
        [
            "id" => 4,
            "caption" => "Purpose",
            "code" => "purpose"
        ],
        [
            "id" => 5,
            "caption" => "Type",
            "code" => "type"
        ],
        [
            "id" => 6,
            "caption" => "Status",
            "code" => "status"
        ],
        [
            "id" => 7,
            "caption" => "Source",
            "code" => "source"
        ],
        [
            "id" => 8,
            "caption" => "Email",
            "code" => "vchr_customer_email"
        ],
        [
            "id" => 9,
            "caption" => "Address",
            "code" => "address"
        ],
        [
            "id" => 10,
            "caption" => "Mobile",
            "code" => "mobile"
        ],
        [
            "id" => 11,
            "caption" => "Created Date",
            "code" => "created_date"
        ],
        [
            "id" => 12,
            "caption" => "Updated Date",
            "code" => "updated_date"
        ],
        [
            "id" => 13,
            "caption" => "Created By",
            "code" => "created_by"
        ],
        [
            "id" => 14,
            "caption" => "Next Followup",
            "code" => "next_followup"
        ],
        [
            "id" => 15,
            "caption" => "Assigned Date",
            "code" => "assigned_date"
        ]
    ];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone(auth()->user()->time_zone ?? 'Asia/Kolkata');
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->timezone(auth()->user()->time_zone ?? 'Asia/Kolkata');
    }

    public static function boot()
    {
        parent::boot();

        static::created(function ($item) {
            if ($item->fk_int_user_id != 1344)
                event(new LeadAdded($item));

            if ($item->fk_int_user_id == MarbleGallery::MARBLE_GALLERY_ID) {
                $settings = Settings::where('vchr_settings_type', 'Lead Prefix')
                    ->select('pk_int_settings_id', 'vchr_settings_value')
                    ->where('fk_int_user_id', $item->fk_int_user_id)
                    ->first();

                if ($settings) {
                    $lastCustomer = Enquiry::select('pk_int_enquiry_id', 'customer_code', 'fk_int_user_id')->where('fk_int_user_id', $item->fk_int_user_id)->latest()->skip(1)->first();
                    if ($lastCustomer->customer_code) {
                        $newCode = self::incrementCode($lastCustomer->customer_code, $settings->vchr_settings_value);
                        $item->customer_code = $newCode;
                        $item->update();

                        if ($item->fk_int_user_id == MarbleGallery::MARBLE_GALLERY_ID) { // custom code for marble gallery
                            $addtionalField = $lastCustomer->additional_details()->where('field_name', 'Customer code')->where('enquiry_id', $item->pk_int_enquiry_id)->first();
                            if ($addtionalField) {
                                $addtionalField->value = $newCode;
                                $addtionalField->save();
                            } else {
                                $additionalDetails = new LeadAdditionalDetails();
                                $additionalDetails->enquiry_id = $item->pk_int_enquiry_id;
                                $additionalDetails->field_id = 352; // Customer code additional field id : 352
                                $additionalDetails->field_name = 'Customer code';
                                $additionalDetails->value = $newCode;
                                $additionalDetails->created_by = $item->fk_int_user_id;
                                $additionalDetails->save();
                            }
                        }

                    } else {
                        $code = '00001';
                        $item->customer_code = $settings->vchr_settings_value . (string)$code;
                        $item->update();

                        if ($item->fk_int_user_id == MarbleGallery::MARBLE_GALLERY_ID) { // custom code for marble gallery
                            $addtionalField = $lastCustomer->additional_details()->where('field_name', 'Customer code')->where('enquiry_id', $item->pk_int_enquiry_id)->first();
                            if ($addtionalField) {
                                $addtionalField->value = $settings->vchr_settings_value . (string)$code;
                                $addtionalField->save();
                            } else {
                                $additionalDetails = new LeadAdditionalDetails();
                                $additionalDetails->enquiry_id = $item->pk_int_enquiry_id;
                                $additionalDetails->field_id = 352; // Customer code additional field id : 352
                                $additionalDetails->field_name = 'Customer code';
                                $additionalDetails->value = $settings->vchr_settings_value . (string)$code;
                                $additionalDetails->created_by = $item->fk_int_user_id;
                                $additionalDetails->save();
                            }
                        }
                    }
                }
            }

        });
    }

    private static function incrementCode($code, $prefix)
    {
        $codeNumber = (int)substr($code, 2);
        $codeNumber++;
        $paddedNumber = str_pad($codeNumber, 5, '0', STR_PAD_LEFT);

        return $prefix . $paddedNumber;
    }

    public static function getCRMUsers($type, $mobileno, $userid, $request = null)
    {
        $vendor_id = User::getVendorIdApi($userid);
        $show = new Common();
        $enquiryType = EnquiryType::where('vchr_enquiry_type', $type)->where(function ($where) use ($vendor_id) {
            $where->where('fk_int_user_id', $vendor_id);
            $where->orWhere('vendor_id', $vendor_id);
        })->first();
        if ($enquiryType) {
            $typeId = $enquiryType->pk_int_enquiry_type_id;
        } else {
            $enType = new EnquiryType();
            $enType->vchr_enquiry_type = $type;
            $enType->fk_int_user_id = $vendor_id;
            $enType->vendor_id = $vendor_id;
            $enType->int_status = "1";
            $fl = $enType->save();
            $typeId = $enType->pk_int_enquiry_type_id;
        }

        $feedback_status = FeedbackStatus::where('vchr_status', 'New')->where(function ($where) use ($vendor_id) {
            $where->where('fk_int_user_id', $vendor_id);
        })->first();
        if (!$feedback_status) {
            $feedback_status = new FeedbackStatus();
            $feedback_status->vchr_status = 'New';
            $feedback_status->vchr_color = '#000000';
            $feedback_status->fk_int_user_id = $vendor_id;
            $feedback_status->created_by = $vendor_id;
            $feedback_status->save();
        }
        $statusId = $feedback_status->pk_int_feedback_status_id;

        $mobileno = str_replace('+', '', $mobileno);
        $phoneno = $mobileno;
        $code = null;
        try {
            $getCode = self::splitString($mobileno, 2);
            $code = $getCode[0];
            $phoneno = $getCode[1];
        } catch (\Exception $exp) {
            $phoneno = substr($mobileno, 2);
            $code = null;
        }
        $requestMobile = '%' . $mobileno;
        $mobnoExist = Enquiry::select(
            'pk_int_enquiry_id',
            'feedback_status',
            'vchr_customer_name',
            'vchr_customer_email',
            'vchr_customer_mobile',
            'lead_attension',
            'more_phone_numbers'
        )
            ->where('fk_int_user_id', $vendor_id)
            ->where(function ($where) use ($phoneno, $requestMobile) {
                $where->where('vchr_customer_mobile', 'LIKE', $requestMobile)
                    ->orWhere('more_phone_numbers', 'like', $requestMobile . '%')
                    ->orWhere('mobile_no', $phoneno);
            })->first();

        if (!$mobnoExist) {
            $feedback = new Enquiry();
            $feedback->vchr_customer_mobile = $mobileno;
            $feedback->mobile_no = $phoneno;
            $feedback->country_code = $code;
            $feedback->read_status = 0;
            $feedback->fk_int_user_id = $vendor_id;
            $feedback->fk_int_enquiry_type_id = $typeId;
            $feedback->feedback_status = $statusId;
            $agent = AssignAgent::where('vendor_id', $vendor_id)->first();
            if ($agent && $type != EnquiryType::IVR) {
                $feedback->staff_id = $agent->agent_id;
                $feedback->assigned_date = Carbon::now();
            } else {
                $feedback->staff_id = null;
            }
            $flag = $feedback->save();
            try {
                Enquiry::newLeadFunctions($feedback->pk_int_enquiry_id);
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
            try {
                event(new ApiHistoryPost(1, $feedback->pk_int_enquiry_id, 0, $vendor_id, $typeId, 2));
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
            // $show->showCrmUsersSubscription($userid);
            /**-------------------AUTOMATION_START-------------------WEBHOOK-----------------**/
            $automation_rule_webhook = $show->getRule($vendor_id, 'new_lead', 'webhook', $feedback->fk_int_enquiry_type_id);
            $status = FeedbackStatus::where('pk_int_feedback_status_id', $feedback->feedback_status)->select('vchr_status')->first();
            $post_data = [
                'customer_name' => $feedback->vchr_customer_name,
                'email' => $feedback->vchr_customer_email,
                'status' => ($status) ? $status->vchr_status : "New Status",
                'phone' => $feedback->vchr_customer_mobile,
                'mobile' => $feedback->mobile_no,
                'flag' => "new_lead",
            ];
            if (count($automation_rule_webhook) > 0) {
                foreach ($automation_rule_webhook as $w_hook) {
                    if ($w_hook->webhook_id != NULL) {
                        try {
                            $webHook = $show->getWebHookById($w_hook->webhook_id);
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }

                        if ($webHook) {
                            try {
                                $show->postToWebHook($webHook->url, $post_data);
                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                            }
                        }
                    }
                }
            }
            $user = User::select('email')->find($vendor_id);
            try {
                if (Common::hasEmailNotificationEnabled($vendor_id)) {
                    Mail::to($user->email)->send(new NewLeadAdded($feedback));
                }

            } catch (\Exception $exp) {

            }

            return $feedback->pk_int_enquiry_id;
        } else {
            try {
                event(new ApiHistoryPost(1, $mobnoExist->pk_int_enquiry_id, 1, $vendor_id, $typeId, 2));
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
            /**-------------------AUTOMATION_START-------------------WEBHOOK-----------------**/
            $automation_rule_webhook = $show->getRule($vendor_id, 'new_lead', 'webhook', $mobnoExist->fk_int_enquiry_type_id);
            $status = FeedbackStatus::where('pk_int_feedback_status_id', $mobnoExist->feedback_status)->select('vchr_status')->first();
            $post_data = [
                'customer_name' => $mobnoExist->vchr_customer_name,
                'email' => $mobnoExist->vchr_customer_email,
                'status' => ($status) ? $status->vchr_status : "New Status",
                'phone' => $mobnoExist->vchr_customer_mobile,
                'mobile' => $mobnoExist->mobile_no,
                'flag' => "new_lead",
            ];
            if (count($automation_rule_webhook) > 0) {
                foreach ($automation_rule_webhook as $w_hook) {
                    if ($w_hook->webhook_id != NULL) {
                        $webHook = $show->getWebHookById($w_hook->webhook_id);
                        if ($webHook) {
                            try {
                                $show->postToWebHook($webHook->url, $post_data);
                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                            }
                        }
                    }
                }
            }

            if ($mobnoExist->lead_attension != 1) {
                if (request('Direction') != 'Outbound' && request('callType') == 0) {
                    //Update lead attension
                    $mobnoExist->lead_attension = 1;
                    $mobnoExist->updated_at = now();
                    $mobnoExist->update();

                    $message = $mobnoExist->vchr_customer_name . ' tried to contact via call';
                    if ($message) {
                        try {
                            event(new CreateFollowup($message, EnquiryFollowup::TYPE_NOTE, $mobnoExist->pk_int_enquiry_id, $vendor_id));
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }
                    }
                }
            } else {
                $mobnoExist->updated_at = now();
                $mobnoExist->update();
            }

            /**--WEBHOOK---------------------------------------------**/
            return $mobnoExist->pk_int_enquiry_id;
        }
    }

    public static function getCRMWebsiteUsers($type, $mobileno, $userid, $name, $email, $feedbacks, $countrycode, $companyName, $request = null)
    {
        $vendor_id = User::getVendorIdApi($userid);
        $staff_id = $statusId = $leadTypeId = $fk_int_purpose_id = null;
        $campaign_automation = false;
        $agency = Agency::select('id')->where('token', $request->agency)->first();
        $agency_id = $agency ? $agency->id : null;
        $countrycode = str_replace("+", "", $countrycode);
        $mobnoExist = Enquiry::where('fk_int_user_id', $vendor_id)
            ->where(function ($q) use ($countrycode, $mobileno) {
                $q->where('vchr_customer_mobile', $countrycode . $mobileno)->orWhere('mobile_no', $mobileno);
            })
            ->lockForUpdate()
            ->first();

        $enquiryType = EnquiryType::query()
            ->select('pk_int_enquiry_type_id', 'vchr_enquiry_type', 'fk_int_user_id', 'vendor_id')
            ->where('vchr_enquiry_type', $type)->where(function ($where) use ($vendor_id) {
                $where->where('fk_int_user_id', $vendor_id);
                $where->orWhere('vendor_id', $vendor_id);
            })->first();
        if ($enquiryType) {
            $typeId = $enquiryType->pk_int_enquiry_type_id;
        } else {
            $enType = new EnquiryType();
            $enType->vchr_enquiry_type = $type;
            $enType->fk_int_user_id = $vendor_id;
            $enType->vendor_id = $vendor_id;
            $enType->int_status = "1";
            $fl = $enType->save();
            $typeId = $enType->pk_int_enquiry_type_id;
        }

        if (request()->has('status') && request()->filled('status')) {
            $feedback_status = FeedbackStatus::select('vchr_status', 'fk_int_user_id', 'pk_int_feedback_status_id')
                ->where('vchr_status', $request->status)->where(function ($where) use ($vendor_id) {
                    $where->where('fk_int_user_id', $vendor_id);
                })->first();
            if (!$feedback_status) {
                $feedback_status = new FeedbackStatus();
                $feedback_status->vchr_status = $request->status;
                $feedback_status->vchr_color = '#000000';
                $feedback_status->fk_int_user_id = $vendor_id;
                $feedback_status->created_by = $vendor_id;
                $feedback_status->save();
            }
            $statusId = $feedback_status->pk_int_feedback_status_id;
        }


        if (request()->has('type') && request()->filled('type')) {
            $leadType = LeadType::where('name', $request->type)->where(function ($where) use ($vendor_id) {
                $where->where('vendor_id', $vendor_id);
            })->first();
            if (!$leadType) {
                $leadType = new LeadType();
                $leadType->name = $request->type;
                $leadType->vendor_id = $vendor_id;
                $leadType->created_by = $vendor_id;
                $leadType->save();
            }
            $leadTypeId = $leadType->id;
        }

        if (request()->has('purpose') && request()->filled('purpose')) {
            $purpose = EnquiryPurpose::where('vchr_purpose', $request->purpose)
                ->where('fk_int_user_id', $vendor_id)->first();
            if ($purpose) {
                $fk_int_purpose_id = $purpose->pk_int_purpose_id;
            } else {
                $purpose = new EnquiryPurpose();
                $purpose->vchr_purpose = $request->purpose;
                $purpose->vchr_purpose_description = $request->purpose;
                $purpose->fk_int_user_id = $vendor_id;
                $purpose->created_by = $vendor_id;
                $purpose->save();
                $fk_int_purpose_id = $purpose->pk_int_purpose_id;
            }
        }

        if ($type == 'IVR') {
            $agent = AssignAgent::where('vendor_id', $vendor_id)->first();
            $staff_id = ($agent) ? $agent->agent_id : null;
        }

        if ($request && $request->staff_name) {
            $staff = User::where('vchr_user_name', $request->staff_name)->where('parent_user_id', $vendor_id)->first();
            $staff_id = ($staff) ? $staff->pk_int_user_id : null;
        } else {

        }


        if (!$mobnoExist) {
            DB::beginTransaction();
            $feedback = new Enquiry();
            $feedback->vchr_customer_mobile = $countrycode . $mobileno;
            $feedback->mobile_no = $mobileno;
            $feedback->country_code = $countrycode;
            $feedback->read_status = 0;
            $feedback->agency_id = $agency_id;
            if ($staff_id) {
                $feedback->staff_id = $staff_id;
                $feedback->assigned_date = Carbon::now();
            }
            if ($request && isset($request['date_of_birth']))
                $feedback->date_of_birth = $request->date_of_birth;
            if ($request && isset($request['purpose']))
                $feedback->fk_int_purpose_id = $fk_int_purpose_id;

            $feedback->fk_int_user_id = $vendor_id;
            $feedback->fk_int_enquiry_type_id = $typeId;
            $feedback->feedback_status = $statusId;
            $feedback->vchr_customer_name = $name;
            $feedback->vchr_customer_email = $email;
            $feedback->vchr_enquiry_feedback = $feedbacks;
            $feedback->address = request('address') ?? '';
            $feedback->created_by = request('userid') ?? $userid;
            $feedback->vchr_customer_company_name = $companyName;
            $feedback->lead_type_id = $leadTypeId;
            $flag = $feedback->save();
            DB::commit();

            $enquiry_id = $feedback->pk_int_enquiry_id;
            try {
                event(new ApiHistoryPost(1, $enquiry_id, 0, $vendor_id, $typeId, 2));
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }


            if ($userid == 1265) {
                $enq = Enquiry::select('pk_int_enquiry_id', 'staff_id', 'assigned_date')->find($enquiry_id);
                \Log::info('ApiHistoryPost-staff' . $enq->staff_id);
            }
            if (isset($request->lead_note) && $request->lead_note != '') {
                try {
                    event(new CreateFollowup($request->lead_note, EnquiryFollowup::TYPE_NOTE, $feedback->pk_int_enquiry_id, $vendor_id));
                } catch (\Exception $e) {
                    Log::info($e->getMessage());
                }
            }

            $show = new Common();
            $addional_fields = LeadAdditionalField::where('vendor_id', $vendor_id)->get();
            foreach ($addional_fields as $add_field) {
                try {
                    $modifiedString = str_replace(" ", "_", $add_field->field_name);
                    if (request()->has($add_field->field_name) || request()->has($modifiedString)) {
                        if ($add_field->input_type == 8) {
                            LeadAdditionalDetails::updateOrCreate(['enquiry_id' => $enquiry_id,
                                'field_id' => $add_field->id],
                                ['field_name' => $add_field->field_name, 'value' => json_encode(explode(',', $request[$add_field->field_name] ?? $request[$modifiedString])),
                                    'created_by' => $userid]);
                        } else {
                            LeadAdditionalDetails::updateOrCreate(['enquiry_id' => $enquiry_id,
                                'field_id' => $add_field->id],
                                ['field_name' => $add_field->field_name, 'value' => $request[$add_field->field_name] ?? $request[$modifiedString],
                                    'created_by' => $userid]);
                        }
                    }
                } catch (\Exception $e) {
                    Log::info('Enquiry model additional field issue');
                    Log::info(request()->all());
                }
            }

            /**--AUTOMATION_WHATSAPP---------------------------------------------**/
            $automation_whatsapp = AutomationRule::where('vendor_id', $feedback->fk_int_user_id)
                ->where('trigger', 'new_lead')
                ->where('action', 'whatsapp')
                ->where('enquiry_source_id', $feedback->fk_int_enquiry_type_id)
                ->orderBy('id', 'DESC')
                ->first();

            if ($automation_whatsapp) {
                $whatsappTemplate = WhatsappTemplate::where('pk_int_whatsapp_template_id', $automation_whatsapp->whatsapp_template_id)
                    ->select('text_whatsapp_template_description')->first();
                if ($whatsappTemplate) {
                    $gupshupObj = new GupShup();
                    $credientails = WatsappCredential::where('vendor_id', $feedback->fk_int_user_id)
                        ->where('status', 1)
                        ->where('platform_id', 2)
                        ->first();
                    if ($credientails) {
                        $data = [
                            "api_key" => $credientails->access_key,
                            "from_number" => $credientails->source_mobile_num,
                            "app_name" => $credientails->template_name
                        ];
                        $gupshupObj->sendWatsappMessageIndividal($feedback->country_id ?? '', $feedback->vchr_customer_mobile, str_replace("{{name}}", $feedback->vchr_customer_name, $whatsappTemplate->text_whatsapp_template_description), $data);
                    }
                }
            }

            /**--END WHATSAPP AUTOMATION ---------------------------------------------**/

            /* ----------- Assign agent vise staff assign----- */
            if (request()->has('department')) {
                try {
                    AutomationRule::departmentViseAutoAssign($request, $feedback, $vendor_id);
                } catch (\Exception $e) {
                    Log::info($e->getMessage());
                }

                if ($vendor_id == 2476)
                    AutomationRule::storeDepartmentToField($request, $feedback, $vendor_id);
            }
            /* ----------- End Assign agent vise staff assign----- */

            /**--API AUTOMATION NIKSHAN---------------------------------------------**/
            $source = $feedback->fk_int_enquiry_type_id;
            $post_data = [
                'phone' => $feedback->vchr_customer_mobile,
            ];
            $automation_rule_api = $show->getRule($vendor_id, 'new_lead', 'api', $source);
            if ($automation_rule_api && $automation_rule_api->api != null) {
                try {
                    if ($vendor_id == Variables::NIKSHAN_USER_ID) {
                        // Nikshan automation
                        $enquiryNotif = Enquiry::select('pk_int_enquiry_id', 'staff_id', 'fk_int_user_id')->find($feedback->pk_int_enquiry_id);
                        $usr = $enquiryNotif->assigned_user;
                        $extension = null;
                        if ($usr) {
                            $extension = $usr->userExtension ? $usr->userExtension->extension : null;
                        }
                        if ($extension)
                            $show->postToIvrAutoCall($automation_rule_api->api, $extension, $post_data);
                    }
                } catch (\Exception $e) {
                    Log::info($e->getMessage());
                }
            }
            /**--API---------------------------------------------**/

            /** --------------------webhook Automation-------------------------------- */
            $automation_rule_webhook = $show->getRule($vendor_id, 'new_lead', 'webhook');
            if (count($automation_rule_webhook) > 0) {
                $status = FeedbackStatus::where('pk_int_feedback_status_id', $feedback->feedback_status)->select('vchr_status')->first();
                $post_data = [
                    'customer_name' => $feedback->vchr_customer_name,
                    'email' => $feedback->vchr_customer_email,
                    'status' => ($status) ? $status->vchr_status : 'New Status',
                    'phone' => $feedback->vchr_customer_mobile,
                    'mobile' => $feedback->mobile_no,
                    'flag' => "new_lead",
                ];
                foreach ($automation_rule_webhook as $w_hook) {
                    if ($w_hook->webhook_id != NULL) {
                        try {
                            $webHook = $show->getWebHookById($w_hook->webhook_id);
                        } catch (\Exception $e) {
                            Log::info($e->getMessage());
                        }
                        if ($webHook) {
                            try {
                                $show->postToWebHook($webHook->url, $post_data);
                            } catch (\Exception $e) {
                                Log::info($e->getMessage());
                            }
                        }
                    }
                }
            }
            /** --------------------End Webhook Automation-------------------------------- */

            if ($userid == 1265) {
                $enq = Enquiry::select('pk_int_enquiry_id', 'staff_id', 'assigned_date')->find($enquiry_id);
                \Log::info('webhook-staff' . $enq->staff_id);
            }


            try {
                AutomationFacade::newLeadFunctions($feedback->pk_int_enquiry_id);
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }
            /**--Assigned to Campaign Automation--------------------------------------------- */
            try {
                $automation_rule_campaign = $show->getRule($feedback->fk_int_user_id, 'new_lead', 'add_to_campaign', $feedback->fk_int_enquiry_type_id);

                if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != NULL) {
                    if ($automation_rule_campaign->enquiry_source_id == $feedback->fk_int_enquiry_type_id) {
                        $campaign_automation = true;
                        $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                        $show->addToCampaign($automation_rule_campaign, $feedback, $feedback->fk_int_user_id, $feedback->fk_int_user_id, $cmp->type);
                    }
                }
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
                \Log::info('Automation failed campaign');
            }
            /**-- End Assigned to Campaign Automation ------------------------------------------- */
            //Start: Send Notification
            if ($staff_id) {
                $notification_title = 'You have been assigned a new lead';
                $notification_description = 'Lead Details: ' . $feedback->name_with_number;
                $notification_data = [
                    "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                    "sound" => "default",
                    "page" => "enquiry_details",
                    "id" => (string)$feedback->pk_int_enquiry_id
                ];

                /* ----------Notification---------- */
                $result = Notifications::getUserTokens($staff_id);
                if ($result) {
                    dispatch(new SendNotification($result, $notification_title, $notification_description, $notification_data))->onQueue('notification');
                }
                try {
                    $existPusher = PusherSetting::where('vendor_id', $vendor_id)->active()->first();
                    if ($existPusher) {
                        $message = $notification_title . ' ' . $notification_description;
                        event(new SendPusherNotification($staff_id, $existPusher, $message));
                    }
                } catch (\Exception $e) {
                    Log::info('Push notification error');
                }
                /* ----------End Notification---------- */
            }
            // End Notification
        } else {
            try {
                event(new ApiHistoryPost(1, $mobnoExist->pk_int_enquiry_id, 1, $vendor_id, $typeId, 2));
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }
            //Start: Send duplicate lead Notification
            if ($mobnoExist) {
                $user_name = $mobnoExist->vchr_customer_name . '(' . $mobnoExist->vchr_customer_mobile . ')';
                $source = $mobnoExist->leadSource ? 'via ' . $mobnoExist->leadSource->vchr_enquiry_type : '';
                $notification_title = $user_name . ' tried to contacted via ' . ($type ?? $source);
                $notification_description = 'Lead Details: ' . $mobnoExist->name_with_number;
                $notification_data = [
                    "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                    "sound" => "default",
                    "page" => "enquiry_details",
                    "id" => (string)$mobnoExist->pk_int_enquiry_id
                ];

                try {
                    /* ----------Notification---------- */
                    if ($mobnoExist->staff_id) {
                        $result = Notifications::getUserTokens($mobnoExist->staff_id);
                        if ($result) {
                            dispatch(new SendNotification($result, $notification_title, $notification_description, $notification_data))->onQueue('notification');
                        }
                    }
                    /* ----------End Notification---------- */
                } catch (\Exception $e) {
                    Log::info('error from duplicate lead via pabbly');
                }

                //Update lead attension
                $mobnoExist->lead_attension = 1;
                $mobnoExist->updated_at = now();
                $mobnoExist->update();

                $message = $mobnoExist->vchr_customer_name . ' tried to contacted via ' . ($type ?? $source);
                if ($message) {
                    try {
                        event(new CreateFollowup($message, EnquiryFollowup::TYPE_NOTE, $mobnoExist->pk_int_enquiry_id, $vendor_id));
                    } catch (\Exception $e) {
                        Log::info($e->getMessage());
                    }
                }
            }
            // End duplicate lead notification

            $enquiry_id = $mobnoExist->pk_int_enquiry_id;
        }

        $enq = Enquiry::select('pk_int_enquiry_id', 'staff_id', 'assigned_date', 'fk_int_enquiry_type_id')->find($enquiry_id);

        if (!$enq->staff_id && !$campaign_automation) {
            /**--Assigned to Datapool--------------------------------------------- */
            $automation_rule_campaign = $show->getRule($feedback->fk_int_user_id, 'new_lead', 'add_to_data_pool', $enq->fk_int_enquiry_type_id);

            if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                if ($automation_rule_campaign->enquiry_source_id == $enq->fk_int_enquiry_type_id) {
                    try {
                        $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                        $show->addToCampaign($automation_rule_campaign, $enq, $feedback->fk_int_user_id, $feedback->fk_int_user_id, $cmp->type);

                    } catch (\Exception $e) {
                        Log::info($e->getMessage());
                    }
                }
            } else {
                $enq->staff_id = $userid;
                $enq->assigned_date = Carbon::today();
                $enq->save();
                $User = User::select('vchr_user_name')->find($userid);
                $agent_name = $User ? $User->vchr_user_name : 'Agent Not Exist';
                $note = $agent_name . " has been designated as the lead.";
                event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enq->pk_int_enquiry_id, $vendor_id));
            }
        }

        return $enquiry_id;
    }

    public function custom_field_values()
    {
        return $this->hasMany(CustomFieldValue::class, 'related_id', 'pk_int_enquiry_id');
    }

    public function task(): HasMany
    {
        return $this->hasMany(Task::class, 'enquiry_id', 'pk_int_enquiry_id');
    }

    public function last_task()
    {
        return $this->hasOne(Task::class, 'enquiry_id', 'pk_int_enquiry_id')->orderBy('id', 'DESC')->where('status', 0)->where('task_category_id', 2)->whereNotNull('scheduled_date')->whereNull('campaign_id');
    }

    public function lastCallTask()
    {
        return $this->hasOne(Task::class, 'enquiry_id', 'pk_int_enquiry_id')->select('status', 'enquiry_id', 'task_category_id', 'id', 'scheduled_date')->orderBy('scheduled_date', 'DESC')->where('status', 0)->where('task_category_id', 2)->whereNotNull('scheduled_date');
    }

    public function lastTask()
    {
        return $this->hasOne(Task::class, 'enquiry_id', 'pk_int_enquiry_id')->select('status', 'enquiry_id', 'task_category_id', 'id', 'scheduled_date')->orderBy('scheduled_date', 'DESC')->where('status', 0)->whereNotNull('scheduled_date');
    }

    public function last_followup()
    {
        return $this->hasOne(EnquiryFollowup::class, 'enquiry_id', 'pk_int_enquiry_id')->orderBy('updated_at', 'DESC');
    }

    public function assigned_user()
    {
        return $this->hasOne(User::class, 'pk_int_user_id', 'staff_id');
    }

    public function campaignLead()
    {
        return $this->hasMany('Getlead\Campaign\Models\CampaignLead', 'lead_id', 'pk_int_enquiry_id');
    }

    public function did_number()
    {
        return $this->hasOne(VirtualNumber::class, 'fk_int_user_id', 'fk_int_user_id')
            ->where('type', 'IVR')->where('int_status', 1)
            ->where(function ($where) {
                $where->whereNull('agent_id')
                    ->orWhere('agent_id', auth()->user()->pk_int_user_id)
                    ->orWhereHas('extensions', function ($query) {
                        $query->where('staff_id', auth()->user()->pk_int_user_id);
                    });
            })->orderBy('agent_id', 'DESC');
    }

    public function additional_details()
    {
        return $this->hasMany('App\FrontendModel\LeadAdditionalDetails', 'enquiry_id', 'pk_int_enquiry_id');
    }

    public function additionalDetails(): HasMany
    {
        return $this->hasMany('App\FrontendModel\LeadAdditionalDetails', 'enquiry_id', 'pk_int_enquiry_id')
            ->select('id', 'enquiry_id', 'field_name', 'field_id', 'value');
    }

    public function getDidCallNumberAttribute()
    {
        return $this->virtual_number ? json_encode(['type' => $this->virtual_number->ivr_type, 'number' => $this->virtual_number->ivr_type == 2 ? '+914847110101' : '+' . $this->virtual_number->vchr_virtual_number]) : null;
    }

    public function getNameWithNumberAttribute()
    {
        return $this->vchr_customer_name ? $this->vchr_customer_name . '(+' . str_replace("+", "", $this->vchr_customer_mobile) . ')' : "No Customer Name (+" . str_replace("+", "", $this->vchr_customer_mobile) . ")";
    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    public static function sendNotification(Enquiry $enq): void
    {
        $customer_name = $enq->vchr_customer_name ? $enq->vchr_customer_name : "No Customer Name";
        $notification_title = "New Lead Arrived on your Getlead. " . $customer_name . "(+" . $enq->vchr_customer_mobile . ")";
        $notification_description = "Hey , You Have got a new lead on your Getlead.
Customer : " . $customer_name . "
Number : +" . $enq->vchr_customer_mobile . "
Date and Time : " . Carbon::now()->format('d M Y h:i A');
        $notification_data = [
            "click_action" => "FLUTTER_NOTIFICATION_CLICK",
            "sound" => "default",
            "page" => "enquiry_details",
            "id" => (string)$enq->pk_int_enquiry_id
        ];
        /* ----------Notification---------- */
        $result = Notifications::getUserTokens($enq->fk_int_user_id);
        if ($result) {
            dispatch(new SendNotification($result, $notification_title, $notification_description, $notification_data))->onQueue('notification');
        }
        try {
            $existPusher = PusherSetting::where('vendor_id', $enq->fk_int_user_id)->active()->first();
            if ($existPusher) {
                event(new SendPusherNotification($enq->fk_int_user_id, $existPusher, $notification_title));
                // dispatch(new SendPusherNotification($enq->fk_int_user_id,$existPusher,$notification_title))->onQueue('pusher-notification');
            }
        } catch (\Exception $e) {
            \Log::info('Push notification error');
        }
    }

    public static $ruleUpload = [

        'contacts' => 'required|mimes:csv',

    ];

    public static $messageUpload = [
        'contacts.required' => 'File is required',
        'contacts.mimes' => 'Unsupported file',

    ];

    /**
     * Get all of the sales for the Enquiry
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function sales(): HasMany
    {
        return $this->hasMany(Order::class, 'fk_int_enquiry_id', 'pk_int_enquiry_id');
    }

    /**
     * Get all of the payments for the Enquiry
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function payments(): HasMany
    {
        return $this->hasMany(PaymentCollection::class, 'fk_int_enquiry_id', 'pk_int_enquiry_id');
    }

    /**
     * Get the enquiry_sales associated with the Enquiry
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function enquiry_sales(): HasOne
    {
        return $this->hasOne(EnquirySalesField::class, 'fk_int_enquiry_id', 'pk_int_enquiry_id');
    }

    /**
     * Get the district associated with the Enquiry
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function district()
    {
        return $this->belongsTo(District::class);
    }

    /**
     * Get the district_name associated with the Enquiry
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function getDistrictNameAttribute()
    {
        return $this->district ? $this->district->name : '';
    }

    public static function newLeadFunctions($lead, $is_notify = null)
    {
        $enq = Enquiry::find($lead);

        // New Lead automation with autoassign
        if (!$enq->staff_id) {
            $assignSourceData = AutomationRule::where('trigger', 'new_lead')->where('vendor_id', $enq->fk_int_user_id)->where('action', 'assign')->where('enquiry_source_id', $enq->fk_int_enquiry_type_id)->orderby('id', 'DESC')->first();
            if ($enq->fk_int_user_id == 196) {
                \Log::info('new source automation 1');
                \Log::info($enq);
            }
            if ($assignSourceData) {
                AutomationRule::autoassign($assignSourceData, $lead);
            } else {
                $assignData = AutomationRule::where('trigger', 'new_lead')->where('vendor_id', $enq->fk_int_user_id)->where('action', 'assign')->whereNull('enquiry_source_id')->orderby('id', 'DESC')->first();
                if ($assignData) {
                    AutomationRule::autoassign($assignData, $lead);
                }
            }
        }

        // Source change automation to auto assign
        $assignSourceData = AutomationRule::where('trigger', 'source_change')->where('enquiry_source_id', $enq->fk_int_enquiry_type_id)->where('vendor_id', $enq->fk_int_user_id)->where('action', 'assign')->orderby('id', 'DESC')->first();
        if ($assignSourceData) {
            AutomationRule::autoassign($assignSourceData, $lead);
        }

        //  Source vise automation with task creation
        $automation_rule_task = AutomationRule::where('trigger', 'source_change')->where('enquiry_source_id', $enq->fk_int_enquiry_type_id)->where('vendor_id', $enq->fk_int_user_id)->where('action', 'task')->orderby('id', 'DESC')->first();
        if ($automation_rule_task) {
            if ($automation_rule_task->duration) {
                $scheduled_date = Carbon::now()->addMinutes($automation_rule_task->duration);
            } else {
                $scheduled_date = Carbon::now();
            }
            $input = [
                'name' => $automation_rule_task->task_title,
                'description' => $automation_rule_task->task_description,
                'scheduled_date' => $scheduled_date,
                'task_category_id' => $automation_rule_task->task_category_id,
                'assigned_to' => $automation_rule_task->task_assigned_to,
                'assigned_by' => $enq->fk_int_user_id,
                'vendor_id' => $enq->fk_int_user_id,
                'enquiry_id' => $enq->pk_int_enquiry_id,
                'status' => 0,
            ];
            Task::create($input);
        }

        // Status change automation
        $assignStatus = AutomationRule::where('trigger', 'status_change')->where('vendor_id', $enq->fk_int_user_id)->where('feedback_status_id', $enq->feedback_status)->where('action', 'assign')->orderby('id', 'DESC')->first();
        if ($assignStatus) {
            AutomationRule::autoassign($assignStatus, $enq->pk_int_enquiry_id);
        }

        // Additional field vise automation
        $assignAdditionalData = AutomationRule::where('trigger', 'value_change')
            ->where('vendor_id', $enq->fk_int_user_id)
            ->where('enquiry_source_id', $enq->fk_int_enquiry_type_id)
            ->where('action', 'assign')
            ->orderby('id', 'DESC')
            ->get();

        if (count($assignAdditionalData) > 0) {
            foreach ($assignAdditionalData as $key => $value) {
                $enqAddField = LeadAdditionalDetails::where('enquiry_id', $enq->pk_int_enquiry_id)
                    ->where('field_id', $value->additional_field)
                    ->where('value', $value->additional_field_value)
                    ->first();
                if ($enqAddField) {
                    $leadAdd = $enqAddField;
                } else {
                    $assignAdditionalData->forget($key);
                }
            }
        }

        $assignAdditionalData = $assignAdditionalData->first();

        if ($assignAdditionalData && $leadAdd) {
            if ($leadAdd->field_id == $assignAdditionalData->additional_field && $leadAdd->value == $assignAdditionalData->additional_field_value) {
                AutomationRule::autoassign($assignAdditionalData, $lead);
            }
        }

        return $lead;

    }

    public static function statusChangeFunction($lead)
    {
        $assignStatus = AutomationRule::where('trigger', 'status_change')->where('vendor_id', $lead->fk_int_user_id)->where('feedback_status_id', $lead->feedback_status)->where('action', 'assign')->orderby('id', 'DESC')->first();
        if ($assignStatus) {
            AutomationRule::autoassign($assignStatus, $lead->pk_int_enquiry_id);
        }
    }

    public function feedbackStatus()
    {
        return $this->hasOne(FeedbackStatus::class, 'pk_int_feedback_status_id', 'feedback_status');
    }

    public function enquiryFollowup()
    {
        return $this->hasMany(EnquiryFollowup::class, 'enquiry_id', 'pk_int_enquiry_id');
    }

    public function enquiryFollowupBySystem()
    {
        return $this->hasMany(EnquiryFollowup::class, 'enquiry_id', 'pk_int_enquiry_id')->latest();
    }

    public function deal()
    {
        return $this->hasMany(Deal::class, 'lead_id', 'pk_int_enquiry_id');
    }

    public function leadSource()
    {
        return $this->hasOne(EnquiryType::class, 'pk_int_enquiry_type_id', 'fk_int_enquiry_type_id');
    }

    public function leadPurpose()
    {
        return $this->hasOne(EnquiryPurpose::class, 'pk_int_purpose_id', 'fk_int_purpose_id');
    }

    public function visit()
    {
        return $this->hasMany('App\VisitUser', 'enquiry_id', 'pk_int_enquiry_id')->latest();
    }

    public function createdBy()
    {
        return $this->hasOne(User::class, 'pk_int_user_id', 'created_by');
    }

    function callMaster()
    {
        return $this->hasMany(CallMaster::class, 'caller_number', 'vchr_customer_mobile');
    }

    function ivrCall()
    {
        return $this->hasMany('App\Ivr\Models\Ivr', 'caller_number', 'vchr_customer_mobile');
    }

    public function scopeSearchLegacy($query, $keyword)
    {
        return $query->where(function ($q) use ($keyword) {
            if (Str::contains($keyword, '@')) {
                $q->where('vchr_customer_email', 'LIKE', "%{$keyword}%");
            } else {
                $keyword = Str::endsWith($keyword, '*') ? $keyword : $keyword . '*';
                $q->whereFullText([
                    'vchr_customer_name',
                    'vchr_customer_mobile',
                    'vchr_customer_company_name',
                    'vchr_customer_email',
                    'mobile_no',
                    'more_phone_numbers'
                ], $keyword, ["mode" => "boolean"]);
            }
        });
    }

    public static function splitString($string, $length)
    {
        if (strlen($string) >= $length) {
            $part1 = substr($string, 0, $length);
            $part2 = substr($string, $length);
            return [$part1, $part2];
        } else {
            return ["", $string]; // Return an empty part1 and the original string in part2
        }
    }

    public static function getCRMWebsiteUsersV1($type, $mobileno, $userid, $name, $email, $feedbacks, $countrycode, $companyName, $request = null)
    {
        $vendor_id = User::getVendorIdApi($userid);
        $staff_id = $statusId = $leadTypeId = $fk_int_purpose_id = null;
        $campaign_automation = false;
        $agency = Agency::select('id')->where('token', $request->agency)->first();
        $agency_id = $agency ? $agency->id : null;
        $countrycode = str_replace("+", "", $countrycode);
        $mobnoExist = Enquiry::where('fk_int_user_id', $vendor_id)
            ->where(function ($q) use ($countrycode, $mobileno) {
                $q->where('vchr_customer_mobile', $countrycode . $mobileno)->orWhere('mobile_no', $mobileno);
            })
            ->lockForUpdate()
            ->first();

        $enquiryType = EnquiryType::select('pk_int_enquiry_type_id', 'vchr_enquiry_type', 'fk_int_user_id', 'vendor_id')
            ->where('vchr_enquiry_type', $type)->where(function ($where) use ($vendor_id) {
                $where->where('fk_int_user_id', $vendor_id);
                $where->orWhere('vendor_id', $vendor_id);
            })->first();
        if ($enquiryType) {
            $typeId = $enquiryType->pk_int_enquiry_type_id;
        } else {
            $enType = new EnquiryType();
            $enType->vchr_enquiry_type = $type;
            $enType->fk_int_user_id = $vendor_id;
            $enType->vendor_id = $vendor_id;
            $enType->int_status = "1";
            $fl = $enType->save();
            $typeId = $enType->pk_int_enquiry_type_id;
        }

        if (request()->has('status') && request()->filled('status')) {
            $feedback_status = FeedbackStatus::select('vchr_status', 'fk_int_user_id', 'pk_int_feedback_status_id')
                ->where('vchr_status', $request->status)->where(function ($where) use ($vendor_id) {
                    $where->where('fk_int_user_id', $vendor_id);
                })->first();
            if (!$feedback_status) {
                $feedback_status = new FeedbackStatus();
                $feedback_status->vchr_status = $request->status;
                $feedback_status->vchr_color = '#000000';
                $feedback_status->fk_int_user_id = $vendor_id;
                $feedback_status->created_by = $vendor_id;
                $feedback_status->save();
            }
            $statusId = $feedback_status->pk_int_feedback_status_id;
        }


        if (request()->has('type') && request()->filled('type')) {
            $leadType = LeadType::where('name', $request->type)->where(function ($where) use ($vendor_id) {
                $where->where('vendor_id', $vendor_id);
            })->first();
            if (!$leadType) {
                $leadType = new LeadType();
                $leadType->name = $request->type;
                $leadType->vendor_id = $vendor_id;
                $leadType->created_by = $vendor_id;
                $leadType->save();
            }
            $leadTypeId = $leadType->id;
        }

        if (request()->has('purpose') && request()->filled('purpose')) {
            $purpose = EnquiryPurpose::where('vchr_purpose', $request->purpose)
                ->where('fk_int_user_id', $vendor_id)->first();
            if ($purpose) {
                $fk_int_purpose_id = $purpose->pk_int_purpose_id;
            } else {
                $purpose = new EnquiryPurpose();
                $purpose->vchr_purpose = $request->purpose;
                $purpose->vchr_purpose_description = $request->purpose;
                $purpose->fk_int_user_id = $vendor_id;
                $purpose->created_by = $vendor_id;
                $purpose->save();
                $fk_int_purpose_id = $purpose->pk_int_purpose_id;
            }
        }

        if ($type == 'IVR') {
            $agent = AssignAgent::where('vendor_id', $vendor_id)->first();
            $staff_id = ($agent) ? $agent->agent_id : null;
        }

        if ($request && $request->staff_name) {
            $staff = User::where('vchr_user_name', $request->staff_name)->where('parent_user_id', $vendor_id)->first();
            $staff_id = ($staff) ? $staff->pk_int_user_id : null;
        }


        if (!$mobnoExist) {
            DB::beginTransaction();
            $feedback = new Enquiry();
            $feedback->vchr_customer_mobile = $countrycode . $mobileno;
            $feedback->mobile_no = $mobileno;
            $feedback->country_code = $countrycode;
            $feedback->read_status = 0;
            $feedback->agency_id = $agency_id;
            if ($staff_id) {
                $feedback->staff_id = $staff_id;
                $feedback->assigned_date = Carbon::now();
            }
            if ($request && isset($request['date_of_birth']))
                $feedback->date_of_birth = $request->date_of_birth;
            if ($request && isset($request['purpose']))
                $feedback->fk_int_purpose_id = $fk_int_purpose_id;

            $feedback->fk_int_user_id = $vendor_id;
            $feedback->fk_int_enquiry_type_id = $typeId;
            $feedback->feedback_status = $statusId;
            $feedback->vchr_customer_name = $name;
            $feedback->vchr_customer_email = $email;
            $feedback->vchr_enquiry_feedback = $feedbacks;
            $feedback->address = request('address') ?? '';
            $feedback->created_by = request('userid') ?? $userid;
            $feedback->vchr_customer_company_name = $companyName;
            $feedback->lead_type_id = $leadTypeId;
            $flag = $feedback->save();
            DB::commit();

            $enquiry_id = $feedback->pk_int_enquiry_id;
            try {
                event(new ApiHistoryPost(1, $enquiry_id, 0, $vendor_id, $typeId, 2));
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }

            if (isset($request->lead_note) && $request->lead_note != '') {
                try {
                    event(new CreateFollowup($request->lead_note, EnquiryFollowup::TYPE_NOTE, $feedback->pk_int_enquiry_id, $vendor_id));
                } catch (\Exception $e) {
                    Log::info($e->getMessage());
                }
            }

            $show = new Common();
            $addional_fields = LeadAdditionalField::where('vendor_id', $vendor_id)->get();
            foreach ($addional_fields as $add_field) {
                try {
                    $modifiedString = str_replace(" ", "_", $add_field->field_name);
                    if (request()->has($add_field->field_name) || request()->has($modifiedString)) {
                        if ($add_field->input_type == 8) {
                            LeadAdditionalDetails::updateOrCreate(['enquiry_id' => $enquiry_id,
                                'field_id' => $add_field->id],
                                ['field_name' => $add_field->field_name, 'value' => json_encode(explode(',', $request[$add_field->field_name] ?? $request[$modifiedString])),
                                    'created_by' => $userid]);
                        } else {
                            LeadAdditionalDetails::updateOrCreate(['enquiry_id' => $enquiry_id,
                                'field_id' => $add_field->id],
                                ['field_name' => $add_field->field_name, 'value' => $request[$add_field->field_name] ?? $request[$modifiedString],
                                    'created_by' => $userid]);
                        }
                    }
                } catch (\Exception $e) {
                    Log::info('Enquiry model additional field issue');
                    Log::info(request()->all());
                }
            }

            /**--AUTOMATION_WHATSAPP---------------------------------------------**/
            $automation_whatsapp = AutomationRule::where('vendor_id', $feedback->fk_int_user_id)
                ->where('trigger', 'new_lead')
                ->where('action', 'whatsapp')
                ->where('enquiry_source_id', $feedback->fk_int_enquiry_type_id)
                ->orderBy('id', 'DESC')
                ->first();

            if ($automation_whatsapp) {
                $whatsappTemplate = WhatsappTemplate::where('pk_int_whatsapp_template_id', $automation_whatsapp->whatsapp_template_id)
                    ->select('text_whatsapp_template_description')->first();
                if ($whatsappTemplate) {
                    $gupshupObj = new GupShup();
                    $credientails = WatsappCredential::where('vendor_id', $feedback->fk_int_user_id)
                        ->where('status', 1)
                        ->where('platform_id', 2)
                        ->first();
                    if ($credientails) {
                        $data = [
                            "api_key" => $credientails->access_key,
                            "from_number" => $credientails->source_mobile_num,
                            "app_name" => $credientails->template_name
                        ];
                        $gupshupObj->sendWatsappMessageIndividal($feedback->country_id ?? '', $feedback->vchr_customer_mobile, str_replace("{{name}}", $feedback->vchr_customer_name, $whatsappTemplate->text_whatsapp_template_description), $data);
                    }
                }
            }
            /**--END WHATSAPP AUTOMATION ---------------------------------------------**/

            /* ----------- Assign agent vise staff assign----- */
            if (request()->has('department')) {
                try {
                    AutomationRule::departmentViseAutoAssign($request, $feedback, $vendor_id);
                } catch (\Exception $e) {
                    Log::info($e->getMessage());
                }

                if ($vendor_id == 2476)
                    AutomationRule::storeDepartmentToField($request, $feedback, $vendor_id);
            }
            /* ----------- End Assign agent vise staff assign----- */

            /**--API AUTOMATION NIKSHAN---------------------------------------------**/
            $source = $feedback->fk_int_enquiry_type_id;
            $post_data = [
                'phone' => $feedback->vchr_customer_mobile,
            ];
            $automation_rule_api = $show->getRule($vendor_id, 'new_lead', 'api', $source);
            if ($automation_rule_api && $automation_rule_api->api != null) {
                try {
                    if ($vendor_id == Variables::NIKSHAN_USER_ID) {
                        // Nikshan automation
                        $enquiryNotif = Enquiry::select('pk_int_enquiry_id', 'staff_id', 'fk_int_user_id')->find($feedback->pk_int_enquiry_id);
                        $usr = $enquiryNotif->assigned_user;
                        $extension = null;
                        if ($usr) {
                            $extension = $usr->userExtension ? $usr->userExtension->extension : null;
                        }
                        if ($extension)
                            $show->postToIvrAutoCall($automation_rule_api->api, $extension, $post_data);
                    }
                } catch (\Exception $e) {
                    Log::info($e->getMessage());
                }
            }
            /**--API---------------------------------------------**/

            /** --------------------webhook Automation-------------------------------- */
            $automation_rule_webhook = $show->getRule($vendor_id, 'new_lead', 'webhook');
            if (count($automation_rule_webhook) > 0) {
                $status = FeedbackStatus::where('pk_int_feedback_status_id', $feedback->feedback_status)->select('vchr_status')->first();
                $post_data = [
                    'customer_name' => $feedback->vchr_customer_name,
                    'email' => $feedback->vchr_customer_email,
                    'status' => ($status) ? $status->vchr_status : 'New Status',
                    'phone' => $feedback->vchr_customer_mobile,
                    'mobile' => $feedback->mobile_no,
                    'flag' => "new_lead",
                ];
                foreach ($automation_rule_webhook as $w_hook) {
                    if ($w_hook->webhook_id != NULL) {
                        try {
                            $webHook = $show->getWebHookById($w_hook->webhook_id);
                        } catch (\Exception $e) {
                            Log::info($e->getMessage());
                        }
                        if ($webHook) {
                            try {
                                $show->postToWebHook($webHook->url, $post_data);
                            } catch (\Exception $e) {
                                Log::info($e->getMessage());
                            }
                        }
                    }
                }
            }
            /** --------------------End Webhook Automation-------------------------------- */

            /**--Assigned to Campaign Automation--------------------------------------------- */
            try {
                $automation_rule_campaign = $show->getRule($feedback->fk_int_user_id, 'new_lead', 'add_to_campaign', $feedback->fk_int_enquiry_type_id);
                if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != NULL) {
                    if ($automation_rule_campaign->enquiry_source_id == $feedback->fk_int_enquiry_type_id) {
                        $campaign_automation = true;
                        $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                        $show->addToCampaign($automation_rule_campaign, $feedback, $feedback->fk_int_user_id, $feedback->fk_int_user_id, $cmp->type);
                    }
                }
            } catch (\Exception $e) {
                Log::info($e->getMessage());
                Log::info('Automation failed campaign');
            }
            /**-- End Assigned to Campaign Automation ------------------------------------------- */
            try {
                AutomationFacade::newLeadFunctions($feedback->pk_int_enquiry_id);
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }

            //Start: Send Notification
            if ($staff_id) {
                $notification_title = 'You have been assigned a new lead';
                $notification_description = 'Lead Details: ' . $feedback->name_with_number;
                $notification_data = [
                    "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                    "sound" => "default",
                    "page" => "enquiry_details",
                    "id" => (string)$feedback->pk_int_enquiry_id
                ];

                /* ----------Notification---------- */
                $result = Notifications::getUserTokens($staff_id);
                if ($result) {
                    dispatch(new SendNotification($result, $notification_title, $notification_description, $notification_data))->onQueue('notification');
                }
                try {
                    $existPusher = PusherSetting::where('vendor_id', $vendor_id)->active()->first();
                    if ($existPusher) {
                        $message = $notification_title . ' ' . $notification_description;
                        event(new SendPusherNotification($staff_id, $existPusher, $message));
                    }
                } catch (\Exception $e) {
                    Log::info('Push notification error');
                }
                /* ----------End Notification---------- */
            }
            // End Notification
        } else {
            try {
                event(new ApiHistoryPost(1, $mobnoExist->pk_int_enquiry_id, 1, $vendor_id, $typeId, 2));
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }
            //Start: Send duplicate lead Notification
            if ($mobnoExist) {
                $user_name = $mobnoExist->vchr_customer_name . '(' . $mobnoExist->vchr_customer_mobile . ')';
                $source = $mobnoExist->leadSource ? 'via ' . $mobnoExist->leadSource->vchr_enquiry_type : '';
                $notification_title = $user_name . ' tried to contacted via ' . ($type ?? $source);
                $notification_description = 'Lead Details: ' . $mobnoExist->name_with_number;
                $notification_data = [
                    "click_action" => "FLUTTER_NOTIFICATION_CLICK",
                    "sound" => "default",
                    "page" => "enquiry_details",
                    "id" => (string)$mobnoExist->pk_int_enquiry_id
                ];

                try {
                    /* ----------Notification---------- */
                    if ($mobnoExist->staff_id) {
                        $result = Notifications::getUserTokens($mobnoExist->staff_id);
                        if ($result) {
                            dispatch(new SendNotification($result, $notification_title, $notification_description, $notification_data))->onQueue('notification');
                        }
                    }
                    /* ----------End Notification---------- */
                } catch (\Exception $e) {
                    Log::info('error from duplicate lead via pabbly');
                }

                //Update lead attension
                $mobnoExist->lead_attension = 1;
                $mobnoExist->updated_at = now();
                $mobnoExist->update();

                $message = $mobnoExist->vchr_customer_name . ' tried to contacted via ' . ($type ?? $source);
                if ($message) {
                    try {
                        event(new CreateFollowup($message, EnquiryFollowup::TYPE_NOTE, $mobnoExist->pk_int_enquiry_id, $vendor_id));
                    } catch (\Exception $e) {
                        Log::info($e->getMessage());
                    }
                }
            }
            // End duplicate lead notification

            $enquiry_id = $mobnoExist->pk_int_enquiry_id;
        }

        $enq = Enquiry::select('pk_int_enquiry_id', 'staff_id', 'assigned_date', 'fk_int_enquiry_type_id')->find($enquiry_id);
        if (!$enq->staff_id && !$campaign_automation) {
            /**--Assigned to Datapool--------------------------------------------- */
            $automation_rule_campaign = $show->getRule($feedback->fk_int_user_id, 'new_lead', 'add_to_data_pool', $enq->fk_int_enquiry_type_id);

            if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                if ($automation_rule_campaign->enquiry_source_id == $enq->fk_int_enquiry_type_id) {
                    try {

                        $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                        $show->addToCampaign($automation_rule_campaign, $enq, $feedback->fk_int_user_id, $feedback->fk_int_user_id, $cmp->type);

                    } catch (\Exception $e) {
                        Log::info($e->getMessage());
                    }
                }
            } else {
                $enq->staff_id = $userid;
                $enq->assigned_date = Carbon::today();
                $enq->save();
                $User = User::select('vchr_user_name')->find($userid);
                $agent_name = $User ? $User->vchr_user_name : 'Agent Not Exist';
                $note = $agent_name . " has been designated as the lead.";
                event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enq->pk_int_enquiry_id, $vendor_id));
            }
        }

        return $enquiry_id;
    }
}
