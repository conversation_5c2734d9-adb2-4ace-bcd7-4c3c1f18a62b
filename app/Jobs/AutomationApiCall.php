<?php

declare(strict_types=1);

namespace App\Jobs;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\LeadType;
use App\Common\Scopes\ApplyFilters;
use App\Reports\ExportLeads\Excel\AdditionalFieldFilter;
use App\Reports\ExportLeads\Excel\AssignedPeriodFilter;
use App\Reports\ExportLeads\Excel\BranchFilter;
use App\Reports\ExportLeads\Excel\CampaignFilter;
use App\Reports\ExportLeads\Excel\CreatedPeriodFilter;
use App\Reports\ExportLeads\Excel\EnquiryPurposeFilter;
use App\Reports\ExportLeads\Excel\EnquiryStatusFilter;
use App\Reports\ExportLeads\Excel\EnquiryTypeFilter;
use App\Reports\ExportLeads\Excel\IvrNumberFilter;
use App\Reports\ExportLeads\Excel\LeadTypeFilter;
use App\Reports\ExportLeads\Excel\SortBy;
use App\Reports\ExportLeads\Excel\StaffFilter;
use App\Reports\ExportLeads\Excel\UpdatePeriodFilter;
use App\Reports\ExportLeads\Excel\VendorFilter;
use App\User;
use Exception;
use Getlead\Campaign\Models\LeadCampaign;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AutomationApiCall implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $tries = 3;

    public $backoff = [60, 180, 300]; // 1min, 3min, 5min

    public function __construct(
        public readonly int $enquiryId,
        public readonly int $vendorId,
        public readonly int $automationId
    )
    {
    }

    /**
     * @throws \Exception
     */
    public function handle(): void
    {
        Log::withContext([
            'enquiry_id' => $this->enquiryId,
            'automation_id' => $this->automationId,
            'vendor_id' => $this->vendorId,
        ])->info('Processing Enquiry automation api call');

        $automation = $this->getAutomation();
        $enquiry = $this->getEnquiry();
        try {
            $data = $this->prepareApiBody($enquiry);

            $response = $this->triggerApi($automation->api, $data);

            Log::info('Api call for automation sent successfully', [
                'response_status' => $response->status(),
            ]);
        } catch (Exception $e) {
            Log::error('EnquiryAutomationWebhook failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    private function getAutomation(): AutomationRule
    {
        return AutomationRule::query()
            ->whereKey($this->automationId)
            ->whereVendorId($this->vendorId)
            ->firstOrFail();
    }

    private function getEnquiry(): ?Enquiry
    {
        return Enquiry::query()
            ->select([
                'pk_int_enquiry_id',
                'vchr_customer_name',
                'vchr_customer_email',
                'vchr_customer_email as email',
                'vchr_customer_mobile',
                'vchr_customer_company_name',
                'country_code',
                'mobile_no',
                'fk_int_purpose_id',
                'fk_int_enquiry_type_id',
                'created_at',
                'updated_at',
                'address',
                'assigned_date',
            ])->addSelect([
                'created_user' => User::query()
                    ->select('tbl_users.vchr_user_name')
                    ->whereColumn('tbl_users.pk_int_user_id', 'tbl_enquiries.created_by')
                    ->limit(1),
                'staff_name' => User::query()
                    ->select('tbl_users.vchr_user_name')
                    ->whereColumn('tbl_users.pk_int_user_id', 'tbl_enquiries.staff_id')
                    ->limit(1),
                'lead_type_name' => LeadType::query()
                    ->select('name')
                    ->whereColumn('lead_types.id', '=', 'tbl_enquiries.lead_type_id')
                    ->where('lead_types.vendor_id', '=', $this->vendorId)
                    ->limit(1),
                'vchr_status' => FeedbackStatus::query()
                    ->select('vchr_status')
                    ->whereColumn('tbl_feedback_status.pk_int_feedback_status_id', '=', 'tbl_enquiries.feedback_status')
                    ->limit(1),
                'enquiry_type' => EnquiryType::query()
                    ->select('vchr_enquiry_type')
                    ->whereColumn(
                        'tbl_enquiry_types.pk_int_enquiry_type_id',
                        '=',
                        'tbl_enquiries.fk_int_enquiry_type_id'
                    )
                    ->limit(1),
                'vchr_purpose' => EnquiryPurpose::query()
                    ->select('vchr_purpose')
                    ->whereColumn('tbl_enquiry_purpose.pk_int_purpose_id', '=', 'tbl_enquiries.fk_int_purpose_id')
                    ->limit(1),
            ])
            ->with([
                'additionalDetails' => function ($q): void {
                    $q->select('enquiry_id', 'field_id', 'value', 'field_name')
                        ->orderBy('field_id');
                },
            ])->where('fk_int_user_id', $this->vendorId)
            ->whereKey($this->enquiryId)
            ->firstOrFail();
    }

    /**
     * @return array<string, mixed>
     */
    private function prepareApiBody(Enquiry $enquiry): array
    {
        // Prepare custom fields
        $customFields = [];
        if ($enquiry->additionalDetails) {
            foreach ($enquiry->additionalDetails as $field) {
                $customFields[] = [
                    'field_id' => $field->field_id,
                    'field_name' => $field->field_name,
                    'value' => $field->value,
                ];
            }
        }

        return [
            'enquiry_id' => $enquiry->pk_int_enquiry_id,
            'custom_fields' => $customFields,
        ];
    }

    /**
     * @return \Illuminate\Http\Client\Response
     */
    private function triggerApi(string $url, array $data)
    {
        $response = Http::timeout(15)
            ->retry(3, 100)
            ->post($url, $data);

        if (!$response->successful()) {
            throw new Exception('Webhook failed with status: ' . $response->status());
        }

        return $response;
    }
}
