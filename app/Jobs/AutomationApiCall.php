<?php

declare(strict_types=1);

namespace App\Jobs;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\LeadType;
use App\User;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AutomationApiCall implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    // Constants for configuration
    private const MAX_TRIES = 3;
    private const BACKOFF_DELAYS = [60, 180, 300]; // 1min, 3min, 5min
    private const HTTP_TIMEOUT = 15;
    private const HTTP_RETRY_ATTEMPTS = 3;
    private const HTTP_RETRY_DELAY = 100;

    public $tries = self::MAX_TRIES;
    public $backoff = self::BACKOFF_DELAYS;

    public function __construct(
        public readonly int $enquiryId,
        public readonly int $vendorId,
        public readonly int $automationId
    )
    {
    }

    /**
     * Execute the automation API call job
     *
     * @throws \Exception
     */
    public function handle(): void
    {
        $startTime = microtime(true);

        Log::withContext([
            'enquiry_id' => $this->enquiryId,
            'automation_id' => $this->automationId,
            'vendor_id' => $this->vendorId,
            'attempt' => $this->attempts(),
        ])->info('Processing Enquiry automation api call');

        try {
            $automation = $this->getAutomation();
            $enquiry = $this->getEnquiry();

            // Validate API URL
            if (!$this->isValidUrl($automation->api)) {
                throw new Exception('Invalid API URL: ' . $automation->api);
            }

            $data = $this->prepareApiBody($enquiry);
            $response = $this->triggerApi($automation->api, $data);

            $executionTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::info('Api call for automation sent successfully', [
                'response_status' => $response->status(),
                'execution_time_ms' => $executionTime,
                'data_size' => strlen(json_encode($data)),
            ]);

        } catch (Exception $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::error('EnquiryAutomationWebhook failed', [
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
                'execution_time_ms' => $executionTime,
                'attempt' => $this->attempts(),
                'max_tries' => self::MAX_TRIES,
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Get the automation rule
     */
    private function getAutomation(): AutomationRule
    {
        $automation = AutomationRule::query()
            ->whereKey($this->automationId)
            ->whereVendorId($this->vendorId)
            ->first();

        if (!$automation) {
            throw new Exception("Automation rule not found: ID {$this->automationId} for vendor {$this->vendorId}");
        }

        return $automation;
    }

    /**
     * Get the enquiry with all required data
     */
    private function getEnquiry(): Enquiry
    {
        $enquiry = Enquiry::query()
            ->select([
                'pk_int_enquiry_id',
                'vchr_customer_name',
                'vchr_customer_email',
                'vchr_customer_email as email',
                'vchr_customer_mobile',
                'vchr_customer_company_name',
                'country_code',
                'mobile_no',
                'fk_int_purpose_id',
                'fk_int_enquiry_type_id',
                'created_at',
                'updated_at',
                'address',
                'assigned_date',
            ])->addSelect([
                'lead_type_name' => LeadType::query()
                    ->select('name')
                    ->whereColumn('lead_types.id', '=', 'tbl_enquiries.lead_type_id')
                    ->where('lead_types.vendor_id', '=', $this->vendorId)
                    ->limit(1),
                'vchr_status' => FeedbackStatus::query()
                    ->select('vchr_status')
                    ->whereColumn('tbl_feedback_status.pk_int_feedback_status_id', '=', 'tbl_enquiries.feedback_status')
                    ->limit(1),
                'enquiry_type' => EnquiryType::query()
                    ->select('vchr_enquiry_type')
                    ->whereColumn(
                        'tbl_enquiry_types.pk_int_enquiry_type_id',
                        '=',
                        'tbl_enquiries.fk_int_enquiry_type_id'
                    )
                    ->limit(1),
                'vchr_purpose' => EnquiryPurpose::query()
                    ->select('vchr_purpose')
                    ->whereColumn('tbl_enquiry_purpose.pk_int_purpose_id', '=', 'tbl_enquiries.fk_int_purpose_id')
                    ->limit(1),
            ])
            ->with([
                'additionalDetails' => function ($q): void {
                    $q->select('enquiry_id', 'field_id', 'value', 'field_name')
                        ->orderBy('field_id');
                },
            ])->where('fk_int_user_id', $this->vendorId)
            ->whereKey($this->enquiryId)
            ->first();

        if (!$enquiry) {
            throw new Exception("Enquiry not found: ID {$this->enquiryId} for vendor {$this->vendorId}");
        }

        return $enquiry;
    }

    /**
     * Prepare the API request body with validated and sanitized data
     *
     * @return array<string, mixed>
     */
    private function prepareApiBody(Enquiry $enquiry): array
    {
        $customFields = [];
        if ($enquiry->additionalDetails) {
            foreach ($enquiry->additionalDetails as $field) {
                if ($field->field_name && $field->value !== null) {
                    $customFields[] = [
                        'field_name' => $this->sanitizeString($field->field_name),
                        'field_key' => Str::slug($field->field_name),
                        'value' => $this->sanitizeString($field->value),
                    ];
                }
            }
        }

        return [
            'enquiry_id' => $enquiry->pk_int_enquiry_id,
            'customer_name' => $this->sanitizeString($enquiry->vchr_customer_name),
            'email' => $this->sanitizeEmail($enquiry->vchr_customer_email),
            'phone' => $this->sanitizePhone($enquiry->vchr_customer_mobile),
            'country_code' => $this->sanitizeString($enquiry->country_code),
            'mobile_no' => $this->sanitizePhone($enquiry->mobile_no),
            'company_name' => $this->sanitizeString($enquiry->vchr_customer_company_name),
            'address' => $this->sanitizeString($enquiry->address),
            'status' => $this->sanitizeString($enquiry->getAttribute('vchr_status')),
            'purpose' => $this->sanitizeString($enquiry->getAttribute('vchr_purpose')),
            'source' => $this->sanitizeString($enquiry->getAttribute('enquiry_type')),
            'type' => $this->sanitizeString($enquiry->getAttribute('lead_type_name')),
            'created_at' => $enquiry->created_at?->toJSON(),
            'updated_at' => $enquiry->updated_at?->toJSON(),
            'custom_fields' => $customFields,
        ];
    }

    /**
     * Trigger the API call with enhanced error handling
     *
     * @return \Illuminate\Http\Client\Response
     */
    private function triggerApi(string $url, array $data)
    {
        $response = Http::timeout(self::HTTP_TIMEOUT)
            ->retry(self::HTTP_RETRY_ATTEMPTS, self::HTTP_RETRY_DELAY)
            ->withHeaders([
                'Content-Type' => 'application/json',
                'User-Agent' => 'CRM-Automation/1.0',
                'X-Request-ID' => uniqid('crm_', true),
            ])
            ->post($url, $data);

        if (!$response->successful()) {
            $errorMessage = sprintf(
                'API call failed with status %d. URL: %s, Response: %s',
                $response->status(),
                $url,
                $response->body()
            );

            Log::error('API call failed', [
                'status' => $response->status(),
                'url' => $url,
                'response_body' => $response->body(),
                'response_headers' => $response->headers(),
            ]);

            throw new Exception($errorMessage, $response->status());
        }

        return $response;
    }

    /**
     * Validate if the URL is properly formatted
     */
    private function isValidUrl(string $url): bool
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false &&
               (str_starts_with($url, 'http://') || str_starts_with($url, 'https://'));
    }

    /**
     * Sanitize string data
     */
    private function sanitizeString(?string $value): ?string
    {
        if ($value === null) {
            return null;
        }

        return trim(strip_tags($value));
    }

    /**
     * Sanitize email data
     */
    private function sanitizeEmail(?string $email): ?string
    {
        if ($email === null) {
            return null;
        }

        $sanitized = filter_var(trim($email), FILTER_SANITIZE_EMAIL);
        return filter_var($sanitized, FILTER_VALIDATE_EMAIL) ? $sanitized : null;
    }

    /**
     * Sanitize phone number data
     */
    private function sanitizePhone(?string $phone): ?string
    {
        if ($phone === null) {
            return null;
        }

        // Remove all non-numeric characters except + and spaces
        return preg_replace('/[^0-9+\s]/', '', trim($phone));
    }
}
