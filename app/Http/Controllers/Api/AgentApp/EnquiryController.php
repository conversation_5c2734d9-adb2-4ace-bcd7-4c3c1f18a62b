<?php

namespace App\Http\Controllers\Api\AgentApp;

use App\AutomationRule;
use App\BackendModel\Enquiry;
use App\BackendModel\EnquiryFollowup;
use App\BackendModel\EnquiryPurpose;
use App\BackendModel\EnquiryType;
use App\BackendModel\FeedbackStatus;
use App\BackendModel\LeadType;
use App\BackendModel\SmsTemplateCode;
use App\BackendModel\WhatsappTemplate;
use App\Common\Application;
use App\Common\Common;
use App\Common\Variables;
use App\FrontendModel\LeadAdditionalDetails;
use App\FrontendModel\LeadAdditionalField;
use Getlead\Campaign\Models\LeadCampaign;
use App\User;
use App\WebHook;
use App\Currency;
use Carbon\Carbon;
use Getlead\Messagebird\Common\GupShup;
use Getlead\Messagebird\Models\WatsappCredential;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use JWTAuth;
use App\Common\Notifications;
use App\Common\SingleSMS;
use App\BackendModel\District;
use App\BackendModel\Taluk;
use App\BackendModel\TataModel;
use App\BackendModel\TataCompetingModel;
use App\Task;
use App\TaskHistory;
use Getlead\Sales\Models\EnquirySalesField;
use App\AgentStaff;
use App\BackendModel\EnquiryLocation;
use App\EnquiryFieldCustomise;
use App\BackendModel\GroupUsers;
use App\IvrExtension;
use App\CallStatus;
use App\Reason;
use App\Checkout_note;
use App\CloudTelephonySetting;
use App\Events\LeadAssigned;
use App\Http\Controllers\Guest\BonvoiceController;
use App\Http\Controllers\Guest\VoxbayController;
use App\Deal;
use App\MeetingOutcome;
use App\Traits\EnquiryTrait;
use App\Traits\PosTrait;
use App\VisitUser;
use App\BackendModel\Settings;
use App\Branch;
use App\DealActivity;
use App\DealTask;
use App\Events\CreateFollowup;
use App\AttandanceStatus;
use App\Constants\MarbleGallery;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use App\DealTaskHistory;
use App\Facades\AutomationFacade;
use App\Facades\FileUpload;

/**
 * @group  Agent App
 *
 * APIs for Agent App Integration
 */
class EnquiryController extends Controller
{
    /**
     * Lead Trait
     * <AUTHOR>
     */

    use EnquiryTrait, PosTrait;

    protected $userId;
    protected $vendorId;
    protected $user;
    protected $applicationObj;
    protected $vendor;
    protected $roleId;
    private $builder_enquiry_base = null;

    public function __construct()
    {
        $this->middleware('jwt.auth');
        $this->applicationObj = new Application();
        $this->user = JWTAuth::parseToken()->toUser();
        $this->userId = $this->user->pk_int_user_id;
        $this->roleId = $this->user->int_role_id;
        $this->vendorId = $this->applicationObj->getVendorIdLead($this->user);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    /**
     * Enquiry Purpose Status Source Agents List
     *
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  "enquirypurpose":[
     *   {
     *      "pk_int_purpose_id as id":9,
     *      "vchr_purpose":"abcd",
     *   }
     *   ],
     *  "enquiry_status":
     *    [
     *     {
     *      "pk_int_feedback_status_id as id" :78,
     *      "vchr_status as status" :"adac",
     *     }
     *    ],
     *    "enqury_source":
     *       [
     *          {
     *             "pk_int_enquiry_type_id":23,
     *              "vchr_enquiry_type": "abcs",
     *          }
     *       ],
     *     "enquiry_agents":
     *       [
     *             {
     *         "pk_int_user_id":677,
     *         "vchr_user_name":"abcd"
     *             }
     *       ],
     *    ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function enquiryPurposeStatusList(Request $request)
    {
        if (Cache::get('purpose_status_list' . $this->vendorId)) {
            $data = Cache::get('purpose_status_list' . $this->vendorId);
        } else {
            $branch = Branch::select('id', 'branch')->where('vendor_id', $this->vendorId)->where('status', '1')->orderBy('branch', 'ASC')->get();
            $meeting_outcome = MeetingOutcome::select('id', 'name as type')
                ->where(function ($q) {
                    $q->where('vendor_id', $this->vendorId)
                        ->OrWhere('vendor_id', 0);
                })->where('status', '1')->orderBy('name', 'ASC')->get();
            $call_status = CallStatus::where('vendor_id', $this->vendorId)->select('id', 'name')->get();
            $status = AttandanceStatus::select('id', 'attandance_status as name')->get();
            $currency = Currency::find(Variables::checkCurrencyEnableSetting('currency'));
            $data_pools = LeadCampaign::where('vendor_id', $this->vendorId)->where('type', LeadCampaign::POOL)->select('id', 'name')->get();
            $activity_filter = [
                ['id' => 'all', 'name' => 'All'],
                ['id' => EnquiryFollowup::TYPE_TASK_NEW, 'name' => 'Task Added'],
                ['id' => EnquiryFollowup::TYPE_NEW_LEAD, 'name' => 'Lead Added'],
                ['id' => EnquiryFollowup::TYPE_TASK_HISTORY, 'name' => 'Task Updated'],
                ['id' => EnquiryFollowup::TYPE_DEAL, 'name' => 'New Deal Added'],
                ['id' => EnquiryFollowup::TYPE_NOTE, 'name' => 'Note Added'],
                // ['id' => EnquiryFollowup::TYPE_LOG_CALL,'name' => 'Call Log Added'],
                // ['id' => EnquiryFollowup::TYPE_LOG_EMAIL,'name' => 'Email Log Added'],
                // ['id' => EnquiryFollowup::TYPE_LOG_MEETING,'name' => 'Meeting Log Added'],
                ['id' => EnquiryFollowup::TYPE_TASK, 'name' => 'Followup Added'],
                ['id' => EnquiryFollowup::TYPE_SCHEDULE, 'name' => 'Schedule Added'],
                ['id' => EnquiryFollowup::TYPE_STATUS, 'name' => 'Status Updated'],
                ['id' => EnquiryFollowup::TYPE_EMAIL, 'name' => 'Sent Email'],
                ['id' => EnquiryFollowup::IVR, 'name' => 'IVR Call'],
                ['id' => EnquiryFollowup::ENQ_PURPOSE, 'name' => 'Purpose Updated'],
                ['id' => EnquiryFollowup::TYPE_VOICE_NOTE, 'name' => 'Voice Note Added'],
                ['id' => EnquiryFollowup::TYPE_FILE_NOTE, 'name' => 'File Note Added'],
                ['id' => EnquiryFollowup::TYPE_DOCUMENT, 'name' => 'File document Added'],
                ['id' => EnquiryFollowup::WHATSAPP_HISTORY, 'name' => 'Whatsapp History'],
                ['id' => EnquiryFollowup::SMS_HISTORY, 'name' => 'Sms History'],
                ['id' => EnquiryFollowup::TYPE_ORDER, 'name' => 'New Order'],
                ['id' => EnquiryFollowup::TYPE_CHECKIN, 'name' => 'Check In'],
                ['id' => EnquiryFollowup::TYPE_CHECKOUT, 'name' => 'Check Out'],
            ];

            $enquiry_all_agents = User::active()->where('int_role_id', User::STAFF)->where('parent_user_id', $this->vendorId)->orWhere('pk_int_user_id', $this->vendorId)->select('pk_int_user_id as id', 'vchr_user_name as name')->orderBy('vchr_user_name', 'ASC')->get();

            $data['attendance_status_enabled'] = Variables::checkEnableSettings('attendance-status');
            $data['activity_filter'] = $activity_filter;
            $data['meeting_outcome'] = $meeting_outcome;
            $data['call_status'] = $call_status;
            $data['general_settings_currency'] = $currency ? $currency->currency : 'INR';
            $data['enquiry_all_agents'] = $enquiry_all_agents;
            $data['branch'] = $branch;
            $data['attendance_status'] = $status;
            $data['data_pools'] = $data_pools;

            Cache::put('purpose_status_list' . $this->vendorId, $data, null, true);
        }

        $enquiry_agents = User::where('int_role_id', User::STAFF)->where('parent_user_id', $this->vendorId);
        if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
            $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->pluck('staff_id')->toArray();
            $enquiry_agents = $enquiry_agents->where(function ($where) use ($assignedStafId) {
                $where->where('pk_int_user_id', Auth::user()->pk_int_user_id)->orWhereIn('pk_int_user_id', $assignedStafId);
            });
        }
        if (Auth::user()->int_role_id == Variables::USER || Auth::user()->is_co_admin == 1) {
            if (auth()->user()->pk_int_user_id == 51 || auth()->user()->parent_user_id == 51) {
                $enquiry_agents = $enquiry_agents->where('int_status', 1);
            }
            $enquiry_agents = $enquiry_agents->orWhereIn('pk_int_user_id', [$this->vendorId]);
        }
        $enquiry_agents = $enquiry_agents->select('pk_int_user_id as id', 'vchr_user_name as name')->get();
        $enquiry_purpose = EnquiryPurpose::select('pk_int_purpose_id as id', 'vchr_purpose as purpose')->where('fk_int_user_id', $this->vendorId)->orderBy('vchr_purpose', 'ASC')->get();

        $q['id'] = 0;
        $q['status'] = 'Empty Status';
        $enquiry_status = FeedbackStatus::UsedStatuses($this->vendorId)->select('pk_int_feedback_status_id as id', 'vchr_status as status')->orderBy('vchr_status', 'ASC')->get()->prepend($q);
        $enquiry_source = EnquiryType::OnlyUser()->where('int_status', Variables::ACTIVE)
            ->where(function ($where) {
                $where->where('vendor_id', $this->vendorId)
                    ->orWhere('vendor_id', 0);
            })->select('pk_int_enquiry_type_id as id', 'vchr_enquiry_type as status')->orderBy('vchr_enquiry_type', 'ASC')->get();
        $fields = EnquiryFieldCustomise::where('vendor_id', $this->vendorId)->first();

        if ($fields) {
            $hidden_fields = explode(',', $fields->hidden_fields);
            $required_fields = explode(',', $fields->required_fields);
        } else {
            $hidden_fields = [];
            $required_fields = [];
        }
        $dateBy = collect([
            ['id' => 1, 'name' => 'Created'],
            ['id' => 2, 'name' => 'Updated'],
            ['id' => 3, 'name' => 'Assigned']
        ]);

        $additional_fields = LeadAdditionalField::where('vendor_id', $this->vendorId)->with('additionalPurpose')->select('id', 'field_name', 'input_type', 'values', 'is_required')->get();
        foreach ($additional_fields as $key => $additional_field) {
            if ($additional_field->input_type == 2 || $additional_field->input_type == 8) {
                if ($additional_field->input_type == 1) {
                    $additional_field->field_type = 'Text';
                } elseif ($additional_field->input_type == 2) {
                    $additional_field->field_type = 'Select';
                } elseif ($additional_field->input_type == 8) {
                    $additional_field->field_type = 'MultiSelect';
                }
                $additional_field->values = json_decode($additional_field->values, true);
            }
            if ($additional_field->input_type == 3 || $additional_field->input_type == 5) {
                $dateBy->push(['id' => $additional_field->id, 'name' => $additional_field->field_name]);
            }
            $additional_field->purpose_id = $additional_field->additionalPurpose?->count() ? $additional_field->additionalPurpose->first()->fk_int_purpose_id : null;
        }


        $data['enquiry_purpose'] = $enquiry_purpose;
        $data['enquiry_status'] = $enquiry_status;
        $data['enquiry_source'] = $enquiry_source;
        $data['enquiry_agents'] = $enquiry_agents;
        $data['hidden_fields'] = $hidden_fields;
        $data['required_fields'] = $required_fields;
        $data['additional_fields'] = $additional_fields;
        $data['date_by'] = $dateBy;

        return $this->response(200, false, null, $data);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    /**
     * Add Enquiry Details
     *
     * @authenticated
     *
     * @bodyParam customer_name string  required  Customer Name. Example: abcd
     * @bodyParam country_code string  required  Country Code. Example: IND
     * @bodyParam mobile_no integer required  Mobile Number. Example: 808789988
     * @bodyParam enq_status_id integer  required  Enquiry Status Id. Example: 22
     * @bodyParam source_id integer required  Source ID. Example: 8
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function store(Request $request)
    {
        Log::info('Store enquiry request from mobile app', ['request' => $request->all()]);
        $data = [];
        $validate_fields = [
            'country_code' => ['required'],
            'mobile_no' => ['required'],
            'source_id' => ['required', 'numeric'],
        ];

        $validate_messages = [
            'country_code.required' => 'Country Code Required',
            'mobile_no.required' => 'Mobile number Required',
        ];

        if ($this->vendorId == Variables::APOLO_USER_ID) {
            $validate_fields = [
                'customer_name' => ['required'],
                'country_code' => ['required'],
                'mobile_no' => ['required'],
                'enq_status_id' => ['required', 'numeric'],
                'source_id' => ['required', 'numeric'],
                'lead_type_id' => ['required', 'numeric'],
                'purchase_date' => ['required'],
                'function_date' => ['required'],
                'exp_wt_grams' => ['required'],
            ];
            $validate_messages = [
                'customer_name.required' => 'Customer name Required',
                'country_code.required' => 'Country Code Required',
                'mobile_no.required' => 'Mobile number Required',
                'enq_status_id.required' => 'Enquiry status Required',
                'lead_type_id.required' => 'Lead Type Required',
                'purchase_date.required' => 'Purchase date Required',
                'function_date.required' => 'Purchase date Required',
                'exp_wt_grams.required' => 'Expected weight Required',
            ];
        }
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {

            return $validated;
        }

        DB::beginTransaction();
        try {
            $request->merge([
                'country_code' => str_replace("+", "", $request->country_code)
            ]);

            $exist = Enquiry::query()
                ->where('fk_int_user_id', $this->vendorId)
                ->where('vchr_customer_mobile', $request->country_code . $request->mobile_no)
                ->first();
            if ($exist) {
                if (request('version') == 'lite') {
                    $data['enuquiry_id'] = $exist->pk_int_enquiry_id;
                    $data['customer_name'] = $exist->vchr_customer_name ?? '';
                    $data['customer_mobile'] = $exist->vchr_customer_mobile;
                    $data['purpose'] = $exist->leadPurpose ? $exist->leadPurpose->vchr_purpose : '';
                    $data['staff_name'] = $exist->assigned_user ? $exist->assigned_user->vchr_user_name : '';
                    if ($this->roleId == User::USERS || $this->user->is_coadmin) {
                        $data['is_open'] = true;
                    } else {
                        $data['is_open'] = $exist->staff_id == auth()->user()->pk_int_user_id ? true : false;
                    }

                    return $this->response(200, true, 'Mobile number already exist', $data);
                }

                return $this->response(200, true, 'Mobile number already exist', null);
            }

            $statusId = $request->enq_status_id;
            if (!$request->has('enq_status_id')) {
                $feedback_status = FeedbackStatus::where('vchr_status', 'New')
                    ->where('fk_int_user_id', $this->vendorId)
                    ->first();
                if (!$feedback_status) {
                    $feedback_status = new FeedbackStatus();
                    $feedback_status->vchr_status = 'New';
                    $feedback_status->vchr_color = '#000000';
                    $feedback_status->fk_int_user_id = $this->vendorId;
                    $feedback_status->created_by = $this->vendorId;
                    $feedback_status->save();
                }
                $statusId = $feedback_status->pk_int_feedback_status_id;
            }

            $enquiry = new Enquiry();
            $enquiry->vchr_customer_name = $request->customer_name;
            $enquiry->country_code = $request->country_code;
            $enquiry->taluk_id = $request->taluk_id;
            $enquiry->district_id = $request->district_id;
            $enquiry->model_id = $request->model_id;
            $enquiry->purchase_plan = $request->purchase_plan;
            $enquiry->date_of_purchase = $request->date_of_purchase;
            $enquiry->live_deal = $request->live_deal;
            $enquiry->remarks = $request->remarks;
            $enquiry->competing_model = $request->competing_model_id;
            $enquiry->mobile_no = $request->mobile_no;
            $enquiry->vchr_customer_company_name = $request->company_name;
            $enquiry->vchr_customer_mobile = $request->country_code . $request->mobile_no;
            $enquiry->feedback_status = $statusId;
            $enquiry->read_status = Variables::DEACTIVE;
            $enquiry->int_status = Variables::ACTIVE;
            $enquiry->fk_int_enquiry_type_id = $request->source_id;
            $enquiry->vchr_enquiry_feedback = $request->note;
            $enquiry->created_by = $this->userId;
            $enquiry->fk_int_user_id = $this->vendorId;
            if ($request->staff_id) {
                $enquiry->staff_id = $request->staff_id;
                $enquiry->assigned_date = Carbon::now();
            }

            $enquiry->vchr_enquiry_feedback = $request->feedback;
            $enquiry->vchr_customer_email = $request->email;
            $enquiry->lead_type_id = $request->lead_type_id;
            $enquiry->purchase_date = $request->purchase_date;
            $enquiry->exp_wt_grams = $request->exp_wt_grams;
            $enquiry->address = $request->address;
            $enquiry->function_date = $request->function_date;
            $enquiry->fk_int_purpose_id = $request->purpose_id;
            $enquiry->landline_number = $request->landline_no;
            $enquiry->more_phone_numbers = $request->more_phone_numbers;
            $enquiry->save();

            $data['id'] = $enquiry->pk_int_enquiry_id;

            try {
                if (!$request->staff_id) {
                    AutomationFacade::newLeadFunctions($enquiry->pk_int_enquiry_id);
                    event(new LeadAssigned($enquiry->pk_int_enquiry_id, $this->userId));
                }
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }

            if ($request->lead_type_id) {
                $lead_type = LeadType::where('id', $request->lead_type_id)->first();
                if ($lead_type && $lead_type->name == 'Key Person') {
                    $exist = EnquiryType::query()
                        ->where('vendor_id', $this->vendorId)
                        ->where('fk_int_enquiry_id', $enquiry->pk_int_enquiry_id)
                        ->first();
                    if (!$exist) {
                        $enquiry_source = new EnquiryType;
                        $enquiry_source->vendor_id = $this->vendorId;
                        $enquiry_source->vchr_enquiry_type = $request->customer_name;
                        $enquiry_source->fk_int_user_id = $this->vendorId;
                        $enquiry_source->fk_int_enquiry_id = $enquiry->pk_int_enquiry_id;
                        $enquiry_source->int_status = Variables::ACTIVE;
                        $enquiry_source->created_by = $request->user()->pk_int_user_id;
                        $enquiry_source->save();
                    }
                }
            }
            if ($request->additional_field) {
                $additional_flds = $request->additional_field;
                if (is_array($additional_flds)) {
                    foreach ($additional_flds as $index => $value) {
                        if ($value != "") {
                            $field_name = LeadAdditionalField::find($index);
                            if ($field_name) {
                                $additionalDetails = new LeadAdditionalDetails();
                                $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                                $additionalDetails->field_id = $index;
                                $additionalDetails->field_name = $field_name->field_name;
                                if ($field_name->input_type == 8) {// Multi Select DropDown
                                    $additionalDetails->value = json_encode(explode(",", $value));
                                } else {
                                    $additionalDetails->value = $value;
                                }
                                $additionalDetails->created_by = $this->userId;
                                $additionalDetails->save();
                            }

                        }

                    }
                } else {
                    $fields = json_decode($additional_flds, TRUE);
                    foreach ($fields as $index => $value) {
                        if ($value != "") {
                            $id = str_replace("custom_field_", "", $index);
                            $field_name = LeadAdditionalField::find($id);
                            if ($field_name) {
                                $additionalDetails = new LeadAdditionalDetails();
                                $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                                $additionalDetails->field_id = $id;
                                $additionalDetails->field_name = $field_name->field_name;
                                if ($field_name->type_text == 'Image') {
                                    $filename = $value->store('public/custom_field_image');
                                    $additionalDetails->value = $filename;
                                } elseif ($field_name->input_type == 8) {// Multi Select DropDown
                                    $additionalDetails->value = json_encode(explode(",", $value));
                                } else {
                                    $additionalDetails->value = $value;
                                }
                                $additionalDetails->created_by = $this->userId;
                                $additionalDetails->save();
                            }

                        }

                    }
                }
            } else {
                $fields = LeadAdditionalField::where('vendor_id', $this->vendorId)->get();
                foreach ($fields as $additional_field) {
                    if ($request['custom_field_' . $additional_field->id]) {
                        $additionalDetails = new LeadAdditionalDetails();
                        $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                        $additionalDetails->field_id = $additional_field->id;
                        $additionalDetails->field_name = $additional_field->field_name;
                        if ($additional_field->type_text == 'Image') {
                            $filename = $request['custom_field_' . $additional_field->id]->store('public/custom_field_image');
                            $additionalDetails->value = $filename;
                        } elseif ($additional_field->input_type == 8) {// Multi Select DropDown
                            $additionalDetails->value = json_encode(explode(",", $request['custom_field_' . $additional_field->id]));
                        } else {
                            $additionalDetails->value = $request['custom_field_' . $additional_field->id];
                        }
                        $additionalDetails->created_by = $this->userId;
                        $additionalDetails->save();
                    }
                }
            }

            try {
                event(new CreateFollowup($enquiry->fk_int_enquiry_type_id, EnquiryFollowup::TYPE_NEW, $enquiry->pk_int_enquiry_id, $this->userId));
                event(new CreateFollowup($request->enq_status_id, EnquiryFollowup::TYPE_STATUS, $enquiry->pk_int_enquiry_id, $this->userId));

            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }

            // Start Add Followup
            if ($request->followup_date) {
                if ($request->followup_date != " " && $request->followup_time != "")
                    $date_time = Carbon::createFromFormat('Y-m-d', $request->followup_date)->toDateString() . ' ' . Carbon::createFromFormat('h:i A', $request->followup_time)->toTimeString();
                elseif ($request->followup_date != " ")
                    $date_time = Carbon::createFromFormat('Y-m-d', $request->followup_date)->toDateString() . ' ' . Carbon::now()->toTimeString();
                else
                    $date_time = now();

                $input = [
                    'name' => 'Follow Up',
                    'description' => '',
                    'scheduled_date' => $date_time,
                    'task_category_id' => 2,
                    'assigned_to' => $enquiry->staff_id,
                    'assigned_by' => $this->userId,
                    'vendor_id' => $this->vendorId,
                    'enquiry_id' => $enquiry->pk_int_enquiry_id,
                    'status' => 0,
                ];
                Task::create($input);
            }
            // End Add Followup
            DB::commit();

            $enquiryType = EnquiryType::where('pk_int_enquiry_type_id', $request->source_id)->first()->vchr_enquiry_type;

            /**--TASK---------------------------------------------**/
            $automation_rule_task = $this->applicationObj->getRule($enquiry->fk_int_user_id, 'new_lead', 'task', '');
            if ($automation_rule_task) {
                $input = [
                    'name' => $automation_rule_task->task_title,
                    'description' => $automation_rule_task->task_description,
                    'scheduled_date' => Carbon::tomorrow(),
                    'task_category_id' => $automation_rule_task->task_category_id,
                    'assigned_to' => $automation_rule_task->task_assigned_to,
                    'assigned_by' => $this->vendorId,
                    'vendor_id' => $this->vendorId,
                    'enquiry_id' => $enquiry->pk_int_enquiry_id,
                    'status' => 0,
                ];
                Task::create($input);
            }
            /**--TASK---------------------------------------------**/
            /**--WHATSAPP---------------------------------------------**/
            $automation_whatsapp = AutomationRule::where('vendor_id', $enquiry->fk_int_user_id)
                ->where('trigger', 'new_lead')
                ->where('action', 'whatsapp')
                ->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)
                ->orderBy('id', 'DESC')
                ->first();

            if ($automation_whatsapp) {
                $whatsappTemplate = WhatsappTemplate::where('pk_int_whatsapp_template_id', $automation_whatsapp->whatsapp_template_id)
                    ->select('text_whatsapp_template_description')->first();
                if ($whatsappTemplate) {
                    $gupshupObj = new GupShup();
                    $credientails = WatsappCredential::where('vendor_id', $enquiry->fk_int_user_id)
                        ->where('status', 1)
                        ->where('platform_id', 2)
                        ->first();
                    if ($credientails) {
                        $data = [
                            "api_key" => $credientails->access_key,
                            "from_number" => $credientails->source_mobile_num,
                            "app_name" => $credientails->template_name
                        ];
                        $gupshupObj->sendWatsappMessageIndividal($enquiry->country_id ?? '', $enquiry->vchr_customer_mobile, str_replace("{{name}}", $enquiry->vchr_customer_name, $whatsappTemplate->text_whatsapp_template_description), $data);
                    }
                }
            }
            /**--WHATSAPP---------------------------------------------**/

            /**--Assigned to Campaign--------------------------------------------- */
            try {
                $automation_rule_campaign = $this->applicationObj->getRule($this->vendorId, 'new_lead', 'add_to_campaign', '');
                if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != NULL) {
                    if ($automation_rule_campaign->enquiry_source_id == $enquiry->fk_int_enquiry_type_id) {
                        $show = new Common();
                        $leads = Enquiry::find($enquiry->pk_int_enquiry_id);
                        $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                        $show->addToCampaign($automation_rule_campaign, $leads, $this->userId, $this->vendorId, $cmp->type);
                    }
                }
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }

            /** __________AUTOMATION_WEBHOOK_________**/
            $automation_rule = $this->applicationObj->getRule($enquiry->fk_int_user_id, 'new_lead', 'webhook', $enquiry->fk_int_enquiry_type_id);
            if (count($automation_rule) > 0) {
                foreach ($automation_rule as $w_hook) {
                    if ($w_hook->webhook_id != NULL) {
                        $status = FeedbackStatus::where('pk_int_feedback_status_id', $enquiry->feedback_status)->select('vchr_status')->first();
                        $post_data = [
                            'customer_name' => $enquiry->vchr_customer_name,
                            'email' => $enquiry->vchr_customer_email,
                            'status' => ($status) ? $status->vchr_status : "New Status",
                            'phone' => $enquiry->vchr_customer_mobile,
                            'mobile' => $enquiry->mobile_no,
                            'flag' => "new_lead",
                        ];
                        $webHook = WebHook::select('url')->where('id', $w_hook->webhook_id)->first();
                        if ($webHook) {
                            try {
                                $this->applicationObj->postToWebHook($webHook->url, $post_data);
                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                            }
                        }
                    }
                }
            }
            /** __________AUTOMATION_WEBHOOK_________**/

            $enq = Enquiry::find($enquiry->pk_int_enquiry_id);
            if (!$enq->staff_id) {
                /**--Assigned to Datapool--------------------------------------------- */
                $show = new Common();
                $automation_rule_campaign = $show->getRule($this->vendorId, 'new_lead', 'add_to_data_pool', $enq->fk_int_enquiry_type_id);

                if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                    if ($automation_rule_campaign->enquiry_source_id == $enq->fk_int_enquiry_type_id) {
                        try {

                            $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                            $show->addToCampaign($automation_rule_campaign, $enq, Auth::user()->pk_int_user_id, $this->vendorId, $cmp->type);

                        } catch (\Exception $e) {
                            Log::info($e->getMessage());
                        }
                    }
                } else {
                    $enq->staff_id = $this->userId;
                    $enq->save();
                }

            }
            /**-- Assigned to Campaign ------------------------------------------- */

            $userObject = User::getUserDetails($this->vendorId);
            $userAdminObject = User::getSingleAdminDetails();
            //Notifications
            $notifications = new Notifications();
            $from = env('MAIL_FROM_ADDRESS');
            $to = $userObject->email;
            $subject = "New Leads Notifications";
            $name = $userObject->vchr_user_name;
            $logo = $userAdminObject->vchr_logo;
            $attachment = "";
            $telegramId = $userObject->telegram_id;
            $mobileNumber = $userObject->vchr_user_mobile;
            $defaultSenderIdAdmin = SingleSMS:: getSenderid($userAdminObject->pk_int_user_id, '');
            $defaultRouteAdmin = SingleSMS:: getRoute($userAdminObject->pk_int_user_id, '');
            $content1 = 'New Leads via  ' . $enquiryType . '-' . $request->customer_name . '( ' . $request->mobile_no . ' ). Added By ' . auth()->user()->vchr_user_name;
            $content2 = 'You have new leads via ' . $enquiryType . '-' . $request->customer_name . '( ' . $request->mobile_no . ' ). Added By ' . auth()->user()->vchr_user_name;
            $dataSend['message'] = $content1;
            $dataSend['user_id'] = $enq->staff_id ?? null;
            $dataSend['page'] = 'lead_api';
            $notifications->notifications($from, $to, $subject, $name, $content1, $content2, $logo, $attachment, $telegramId, $this->vendorId, $mobileNumber, $defaultRouteAdmin, $defaultSenderIdAdmin, $dataSend);
            //-----------------
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::info("Error Occurred While Adding Enquiry through API:" . $exception->getMessage());

            return $this->response(200, false, $exception->getMessage(), null);
        }

        return $this->response(200, false, 'success', $data);
    }
    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    /**
     * Enquiry Details
     *
     * @authenticated
     *
     * @bodyParam id integer  required  Id. Example: 25
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function show(Request $request)
    {
        Log::info('Get enquiry by id request from mobile app ', ["request" => $request->all()]);

        $validate_fields = [
            'id' => ['required', 'numeric']
        ];
        $validate_messages = [
            'id.required' => 'Enquiry Id Required',
            'id.numeric' => 'Enquiry Id should be numeric'
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }

        $enquiry = Enquiry::where('pk_int_enquiry_id', $request->id)
            ->where('tbl_enquiries.fk_int_user_id', $this->vendorId)
            ->select('vchr_enquiry_feedback', 'tbl_enquiries.created_by', 'tbl_enquiries.fk_int_enquiry_type_id',
                'tbl_enquiries.fk_int_user_id', 'staff_id', 'tbl_enquiries.fk_int_enquiry_type_id', \DB::raw('IFNULL(lead_types.name,"") as lead_type'), 'purchase_date', 'exp_wt_grams',
                'pk_int_enquiry_id', 'read_status', \DB::raw('IFNULL(tbl_enquiries.vchr_customer_name,"No Customer Name") as vchr_customer_name'),
                'date_of_birth', 'vchr_customer_company_name', 'country_code', 'mobile_no', 'vchr_customer_mobile',
                'vchr_customer_email', 'tbl_enquiry_purpose.pk_int_purpose_id',
                \DB::raw('IFNULL(tbl_enquiry_purpose.vchr_purpose,"None") as vchr_purpose'), 'tbl_enquiries.created_at',
                'feedback_status', 'vchr_status', 'address', 'function_date', 'tbl_enquiries.lead_type_id',
                'tbl_districts.name AS district_name', 'tbl_districts.id as district_id', 'tbl_enquiries.next_follow_up',
                'tbl_enquiries.model_id', 'tbl_enquiries.purchase_plan', 'tbl_enquiries.date_of_purchase',
                'tbl_enquiries.live_deal', 'tbl_enquiries.remarks', 'tbl_enquiries.competing_model',
                'tbl_enquiries.landline_number', \DB::raw('tbl_enquiries.more_phone_numbers')
            )
            ->with(['did_number:pk_int_virtual_number_id,fk_int_user_id,vchr_virtual_number,uid,pin', 'deal', 'assigned_user'])
            ->leftJoin('tbl_enquiry_purpose', 'tbl_enquiries.fk_int_purpose_id', '=', 'tbl_enquiry_purpose.pk_int_purpose_id')
            ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id')
            ->leftJoin('lead_types', 'tbl_enquiries.lead_type_id', '=', 'lead_types.id')
            ->leftJoin('tbl_districts', 'tbl_enquiries.district_id', '=', 'tbl_districts.id')
            ->first();

        if ($enquiry) {

            $enquiry['deal_count'] = $enquiry?->deal->count();
            $enquiry['staff_name'] = $enquiry?->assigned_user?->vchr_user_name;
            $enquiry->makeHidden(['assigned_user', 'deal']);

            $additional_fields = DB::table('enquiry_additional_details')->where('enquiry_id', $request->id)
                ->join('lead_additional_fields', 'lead_additional_fields.id', '=', 'enquiry_additional_details.field_id')
                ->leftJoin('additional_purposes', 'additional_purposes.fk_int_additional_field_id', '=', 'lead_additional_fields.id')
                ->select('enquiry_additional_details.*', 'lead_additional_fields.input_type', 'additional_purposes.fk_int_purpose_id as purpose_id')
                ->get();

            $enquiry->additionalfields = $additional_fields;
            $enquiry->more_phone_numbers = empty($enquiry->more_phone_numbers) ? "[]" : (strpos($enquiry->more_phone_numbers, "[") != false ? $enquiry->more_phone_numbers : ($enquiry->more_phone_numbers == "" || $enquiry->more_phone_numbers == null || $enquiry->more_phone_numbers == "null" ? "[]" : json_encode(explode(",", $enquiry->more_phone_numbers))));
            $data['enquiry_details'] = $enquiry;

            //Get Next Enquiry
            $select_fields = [
                'tbl_enquiries.fk_int_user_id', 'tbl_enquiries.vchr_customer_mobile', 'pk_int_enquiry_id',
                \DB::raw('IFNULL(tbl_enquiries.vchr_customer_name,"No Customer Name") as vchr_customer_name'),
                \DB::raw('IFNULL(vchr_purpose,"None") as vchr_purpose'), 'tbl_feedback_status.vchr_status',
                'tbl_enquiries.created_at', 'tbl_enquiries.mobile_no',
                \DB::raw('IFNULL(tbl_enquiries.country_code,"") as country_code'), 'tbl_enquiries.landline_number',
                'tbl_enquiries.more_phone_numbers'
            ];

            //Need to add join  with tasks
            if ($request->calling_mode && $request->calling_mode == 1 && (!$request->campaign_id || $request->campaign_id == -1)) {
                $currentTask = Task::NonCampaign()
                    ->where('tasks.vendor_id', $this->vendorId)
                    ->where('tb.pk_int_enquiry_id', '!=', $request->id)
                    ->join('tbl_enquiries as tb', 'tasks.enquiry_id', '=', 'tb.pk_int_enquiry_id')
                    ->leftJoin('tbl_feedback_status', 'tb.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id', 'created_at')
                    ->leftJoin('tbl_enquiry_purpose', 'tb.fk_int_purpose_id', '=', 'tbl_enquiry_purpose.pk_int_purpose_id')
                    ->whereNull('tb.deleted_at')
                    ->select('tasks.scheduled_date', 'pk_int_enquiry_id', 'vchr_customer_name',
                        'tb.fk_int_user_id', 'tb.vchr_customer_mobile', 'pk_int_enquiry_id', \DB::raw('IFNULL(tb.vchr_customer_name,"No Customer Name") as vchr_customer_name'), \DB::raw('IFNULL(tb.country_code,"") as country_code'),
                        'tb.landline_number', 'tb.more_phone_numbers', \DB::raw('IFNULL(vchr_purpose,"None") as vchr_purpose'), 'tbl_feedback_status.vchr_status',)
                    ->where(function ($q) use ($request) {
                        if ($this->roleId == User::STAFF) {
                            $q->where('tasks.assigned_to', $request->user()->pk_int_user_id);
                            // $q->orWhere('tb.staff_id', $request->user()->pk_int_user_id);
                        }
                    })
                    ->where('tasks.status', 0)
                    ->where('tasks.task_category_id', 2)
                    // ->whereBetween('tasks.scheduled_date', [Carbon::now()->addMinutes(-15)->format('Y-m-d H:i:s'), Carbon::now()->addMinutes(15)->format('Y-m-d H:i:s')])
                    ->orderBy('tasks.scheduled_date', 'asc')
                    ->first();
                $next_enquiry = $currentTask;
                $next_enquiry ? $next_enquiry['scheduled_date'] = $next_enquiry?->scheduled_date ?? null : $next_enquiry = json_decode('{}');
            } elseif ($request->campaign_id) {
                $currentTask = Task::where('tasks.vendor_id', $this->vendorId)
                    ->where('tb.pk_int_enquiry_id', '!=', $request->id)
                    ->where('tasks.campaign_id', $request->campaign_id)
                    ->where('tasks.status', 0)
                    ->where('tasks.task_category_id', 2)
                    ->join('tbl_enquiries as tb', 'tasks.enquiry_id', '=', 'tb.pk_int_enquiry_id')
                    ->leftJoin('tbl_feedback_status', 'tb.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id', 'created_at')
                    ->leftJoin('tbl_enquiry_purpose', 'tb.fk_int_purpose_id', '=', 'tbl_enquiry_purpose.pk_int_purpose_id')
                    ->whereNull('tb.deleted_at')
                    ->select('tasks.scheduled_date', 'pk_int_enquiry_id', 'vchr_customer_name',
                        'tb.fk_int_user_id', 'tb.vchr_customer_mobile', 'pk_int_enquiry_id', \DB::raw('IFNULL(tb.vchr_customer_name,"No Customer Name") as vchr_customer_name'), \DB::raw('IFNULL(tb.country_code,"") as country_code'),
                        'tb.landline_number', 'tb.more_phone_numbers', \DB::raw('IFNULL(vchr_purpose,"None") as vchr_purpose'),
                        'tbl_feedback_status.vchr_status')
                    ->where(function ($q) use ($request) {
                        if ($this->roleId == User::STAFF) {
                            $q->where('tasks.assigned_to', $request->user()->pk_int_user_id);
                            $q->where('staff_id', $request->user()->pk_int_user_id);
                        }
                    })
                    ->orderBy('tasks.scheduled_date', 'asc')
                    ->first();

                $next_enquiry = $currentTask;
                $next_enquiry ? $next_enquiry['scheduled_date'] = $next_enquiry?->scheduled_date ?? null : $next_enquiry = json_decode('{}');

            } else {
                $next_enquiry = Enquiry::where('tbl_enquiries.fk_int_user_id', $this->vendorId)
                    ->where('pk_int_enquiry_id', '!=', $request->id)
                    ->with(['did_number'])
                    ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id', 'created_at')
                    ->leftJoin('tbl_enquiry_purpose', 'tbl_enquiries.fk_int_purpose_id', '=', 'tbl_enquiry_purpose.pk_int_purpose_id');

                if ($this->roleId == User::STAFF)
                    $next_enquiry = $next_enquiry->where('staff_id', $request->user()->pk_int_user_id);

                $next_enquiry->select($select_fields)->orderBy('tbl_enquiries.updated_at', 'DESC')->first();
            }
            $data['next_enquiry'] = $next_enquiry;

            //Outstanding balance
            $amount_due = EnquirySalesField::where('fk_int_enquiry_id', $request->id)->first();
            if ($amount_due)
                $data['outstanding_amount'] = $amount_due->balance_amount;
            else
                $data['outstanding_amount'] = 0;

            if (Auth::user()->pk_int_user_id == $enquiry->staff_id || Auth::user()->is_co_admin == 1 || Auth::user()->int_role_id == 2 || AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->count() == 1) {
                $data['edit'] = true;
            } else {
                if (Variables::checkEnableSettings('global-search') && !Variables::checkEnableSettings('number-masking')) {
                    $data['edit'] = true;
                } else {
                    $data['edit'] = false;
                }
            }

            // Tally activation
            $checkTally = Settings::where('vchr_settings_type', 'Tally')
                ->where('fk_int_user_id', $this->vendorId)
                ->where('vchr_settings_value', 'true')
                ->select('pk_int_settings_id')
                ->first();

            $data['tally_active'] = ($checkTally) ? true : false;

            if (Variables::checkEnableSettings('number-masking')) {
                $data['numberMasking'] = true;
            } else {
                $data['numberMasking'] = false;
            }

            return $this->response(200, false, "success", $data);
        } else {
            return $this->response(200, true, "Not Found", null);
        }

    }

    public function showLead(Request $request)
    {
        $validate_fields = [
            'id' => ['required', 'numeric']
        ];
        $validate_messages = [
            'id.required' => 'Enquiry Id Required',
            'id.numeric' => 'Enquiry Id should be numeric'
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }

        $enquiry = Enquiry::where('pk_int_enquiry_id', $request->id)
            ->where('tbl_enquiries.fk_int_user_id', $this->vendorId)
            ->with(['did_number'])
            ->leftJoin('tbl_enquiry_purpose', 'tbl_enquiries.fk_int_purpose_id', '=', 'tbl_enquiry_purpose.pk_int_purpose_id')
            ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id')
            ->leftJoin('lead_types', 'tbl_enquiries.lead_type_id', '=', 'lead_types.id')
            ->leftJoin('tbl_taluks', 'tbl_enquiries.taluk_id', '=', 'tbl_taluks.id')
            ->leftJoin('tbl_districts', 'tbl_enquiries.district_id', '=', 'tbl_districts.id')
            ->select('vchr_enquiry_feedback', 'tbl_enquiries.created_by', 'tbl_enquiries.fk_int_enquiry_type_id', 'tbl_enquiries.fk_int_user_id', 'staff_id', 'tbl_enquiries.fk_int_enquiry_type_id', \DB::raw('IFNULL(lead_types.name,"") as lead_type'), 'purchase_date', 'exp_wt_grams',
                'pk_int_enquiry_id', 'read_status', \DB::raw('IFNULL(tbl_enquiries.vchr_customer_name,"No Customer Name") as vchr_customer_name'), 'date_of_birth', 'vchr_customer_company_name', 'country_code', \DB::raw('IFNULL(mobile_no,vchr_customer_mobile) as mobile_no'), \DB::raw('IFNULL(vchr_customer_mobile,mobile_no) as vchr_customer_mobile'), 'vchr_customer_email', 'tbl_enquiry_purpose.pk_int_purpose_id', \DB::raw('IFNULL(tbl_enquiry_purpose.vchr_purpose,"None") as vchr_purpose'), 'tbl_enquiries.created_at', 'feedback_status', 'vchr_status', 'address', 'function_date', 'tbl_enquiries.lead_type_id', 'tbl_taluks.name AS taluk_name', 'tbl_districts.name AS district_name', 'tbl_districts.id as district_id', 'tbl_enquiries.next_follow_up', 'tbl_enquiries.model_id', 'tbl_enquiries.purchase_plan', 'tbl_enquiries.date_of_purchase', 'tbl_enquiries.live_deal', 'tbl_enquiries.remarks', 'tbl_enquiries.competing_model', 'tbl_enquiries.landline_number', \DB::raw('tbl_enquiries.more_phone_numbers'))
            ->first();

        if ($enquiry) {
            $enquiry['deal_count'] = Deal::where('lead_id', $enquiry->pk_int_enquiry_id)->count();
            $enquiry['staff_name'] = ($enquiry->staff_id) ? User::where('pk_int_user_id', $enquiry->staff_id)->pluck('vchr_user_name')->first() : null;
            Enquiry::where('pk_int_enquiry_id', $enquiry->pk_int_enquiry_id)->update(['read_status' => 1]);

            if ($this->roleId == User::STAFF)
                EnquiryFollowup::where('enquiry_id', $request->id)->where('assigned_to', NULL)
                    ->update(['assigned_to' => $this->userId]);
            EnquiryFollowup::where('enquiry_id', $request->id)->where('created_by', NULL)
                ->update(['created_by' => $this->vendorId]);

            $additional_fields = DB::table('enquiry_additional_details')->where('enquiry_id', $request->id)
                ->join('lead_additional_fields', 'lead_additional_fields.id', '=', 'enquiry_additional_details.field_id')
                ->leftJoin('additional_purposes', 'additional_purposes.fk_int_additional_field_id', '=', 'lead_additional_fields.id')
                ->select('enquiry_additional_details.*', 'lead_additional_fields.input_type', 'additional_purposes.fk_int_purpose_id as purpose_id')
                ->get();

            $enquiry->additionalfields = $additional_fields;
            $enquiry->more_phone_numbers = empty($enquiry->more_phone_numbers) ? "[]" : (strpos($enquiry->more_phone_numbers, "[") != false ? $enquiry->more_phone_numbers : ($enquiry->more_phone_numbers == "" || $enquiry->more_phone_numbers == null || $enquiry->more_phone_numbers == "null" ? "[]" : json_encode(explode(",", $enquiry->more_phone_numbers))));
            $data['enquiry_details'] = $enquiry;

            //Get Next Enquiry
            $select_fields = [
                'tbl_enquiries.fk_int_user_id', 'tbl_enquiries.vchr_customer_mobile', 'pk_int_enquiry_id',
                \DB::raw('IFNULL(tbl_enquiries.vchr_customer_name,"No Customer Name") as vchr_customer_name'), \DB::raw('IFNULL(vchr_purpose,"None") as vchr_purpose'), 'tbl_feedback_status.vchr_status', 'tbl_enquiries.created_at', 'tbl_enquiries.mobile_no', \DB::raw('IFNULL(tbl_enquiries.country_code,"") as country_code'), 'tbl_enquiries.landline_number', 'tbl_enquiries.more_phone_numbers'
            ];

            $next_enquiry = Enquiry::where('tbl_enquiries.fk_int_user_id', $this->vendorId)
                ->where('pk_int_enquiry_id', '!=', $request->id)
                ->with(['did_number'])
                ->leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id', 'created_at')
                ->leftJoin('tbl_enquiry_purpose', 'tbl_enquiries.fk_int_purpose_id', '=', 'tbl_enquiry_purpose.pk_int_purpose_id');

            if ($this->roleId == User::STAFF)
                $next_enquiry = $next_enquiry->where('staff_id', $request->user()->pk_int_user_id);
            //->select();//->first();

            //Need to add join  with tasks
            if ($request->calling_mode && $request->calling_mode == 1 && (!$request->campaign_id || $request->campaign_id == -1)) {
                $task_where = [
                    'tasks.status' => 0,
                    'tasks.task_category_id' => 2,
                ];
                if ($this->roleId == User::STAFF) {
                    $task_where['tasks.assigned_to'] = $request->user()->pk_int_user_id;
                    $task_where['tbl_enquiries.staff_id'] = $request->user()->pk_int_user_id;
                }
                $current_task = Task::NonCampaign()
                    ->where('tbl_enquiries.pk_int_enquiry_id', '!=', $request->id)
                    ->where('tasks.vendor_id', $this->vendorId)
                    ->whereNull('tbl_enquiries.deleted_at')
                    ->join('tbl_enquiries', 'tasks.enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
                    ->select('tasks.*')
                    ->where($task_where)
                    ->orderBy('tasks.scheduled_date', 'ASC')
                    ->whereBetween('tasks.scheduled_date', [Carbon::now()->addMinutes(-15)->format('Y-m-d H:i:s'), Carbon::now()->addMinutes(15)->format('Y-m-d H:i:s')])->first();
                if ($current_task)
                    $next_enquiry->where('pk_int_enquiry_id', $current_task->enquiry_id);
                array_push($select_fields, 'tas.scheduled_date');
                if ($this->roleId == User::STAFF)
                    $staff_fil = 'and t.assigned_to=' . $request->user()->pk_int_user_id;
                else
                    $staff_fil = '';
                $next_enquiry->join(\DB::raw('( select * from tasks t where (t.campaign_id is null or t.campaign_id=0) and t.enquiry_id !=' . $request->id . ' and t.status=0 and t.task_category_id=2 ' . $staff_fil . ' and `vendor_id` = ' . $this->vendorId . ' and t.deleted_at is null order by t.scheduled_date asc ) as tas'), function ($join) use ($request) {
                    $join->on('tbl_enquiries.pk_int_enquiry_id', '=', 'tas.enquiry_id');
                })->select($select_fields)->orderBy('tas.scheduled_date', 'ASC');
            } elseif ($request->campaign_id) {
                $task_where = [
                    'tasks.status' => 0,
                    'tasks.task_category_id' => 2,
                ];
                if ($this->roleId == User::STAFF) {
                    $task_where['tasks.assigned_to'] = $request->user()->pk_int_user_id;
                    $task_where['tbl_enquiries.staff_id'] = $request->user()->pk_int_user_id;
                }
                $current_task = Task::where('tasks.vendor_id', $this->vendorId)
                    ->where('tbl_enquiries.pk_int_enquiry_id', '!=', $request->id)
                    ->where('tasks.campaign_id', $request->campaign_id)
                    ->whereNull('tbl_enquiries.deleted_at')
                    ->join('tbl_enquiries', 'tasks.enquiry_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
                    ->select('tasks.*')->orderBy('tasks.scheduled_date', 'ASC')
                    ->where($task_where)
                    ->first();

                if ($current_task)
                    $next_enquiry->where('pk_int_enquiry_id', $current_task->enquiry_id);
                array_push($select_fields, 'tas.scheduled_date');
                if ($this->roleId == User::STAFF)
                    $staff_fil = 'and t.assigned_to=' . $request->user()->pk_int_user_id;
                else
                    $staff_fil = '';
                $next_enquiry->join(\DB::raw('( select * from tasks t where t.enquiry_id !=' . $request->id . ' and t.status=0 and t.task_category_id=2 and t.campaign_id=' . $request->campaign_id . ' ' . $staff_fil . ' and `vendor_id` = ' . $this->vendorId . ' and t.deleted_at is null order by t.scheduled_date asc ) as tas'), function ($join) use ($request) {
                    $join->on('tbl_enquiries.pk_int_enquiry_id', '=', 'tas.enquiry_id');
                })->select($select_fields)->orderBy('tas.scheduled_date', 'ASC');
            } else {

                $next_enquiry->select($select_fields)->orderBy('tbl_enquiries.updated_at', 'DESC');
            }
            $data['next_enquiry'] = $next_enquiry->first();
            //Outstanding balance
            $amount_due = EnquirySalesField::where('fk_int_enquiry_id', $request->id)->first();
            if ($amount_due)
                $data['outstanding_amount'] = $amount_due->balance_amount;
            else
                $data['outstanding_amount'] = 0;

            if (Auth::user()->pk_int_user_id == $enquiry->staff_id || Auth::user()->is_co_admin == 1 || Auth::user()->int_role_id == 2 || AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)->count() == 1) {
                $data['edit'] = true;
            } else {
                if (Variables::checkEnableSettings('global-search')) {
                    $data['edit'] = true;
                } else {
                    $data['edit'] = false;
                }
            }

            // Tally activation
            $checkTally = Settings::where('vchr_settings_type', 'Tally')
                ->where('fk_int_user_id', $this->vendorId)
                ->where('vchr_settings_value', 'true')
                ->first();

            $data['tally_active'] = ($checkTally) ? true : false;

            return $this->response(200, false, "success", $data);
        } else {
            return $this->response(200, true, "Not Found", null);
        }

    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    /**
     * Update Enquiry
     *
     * @authenticated
     *
     * @bodyParam customer_name string  required  Customer Name. Example: abcd
     * @bodyParam country_code integer  required  Country Code. Example: IND
     * @bodyParam mobile_no integer  required  Mobile Number. Example: 808939499
     * @bodyParam source_id integer  required  Source Id. Example: 8
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function update(Request $request)
    {
        /**
         * @var User $user
         */
        $user = $request->user();

        Log::info('Updating enquiry from mobile app', ['request' => $request->all(), 'user_id' => $user->pk_int_user_id]);

        $validate_fields = [
            'country_code' => ['required'],
            'mobile_no' => ['required'],
            'source_id' => ['required', 'numeric'],
        ];
        $validate_messages = [
            'country_code.required' => 'Country Code Required',
            'mobile_no.required' => 'Mobile number Required',
            'source_id.required' => 'Enquiry Source Required'
        ];

        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }

        $request->merge([
            'country_code' => str_replace('+', '', $request->country_code)
        ]);

        $exist = Enquiry::query()
            ->where('pk_int_enquiry_id', '!=', $request->id)
            ->where('fk_int_user_id', $this->vendorId)
            ->where('vchr_customer_mobile', $request->country_code . $request->mobile_no)
            ->exists();
        if ($exist) {
            return $this->response(200, true, 'Mobile number already exist', null);
        }

        $enquiry = Enquiry::query()->whereKey($request->id)->first();

        $isStaffUpdateSourceAllowed = Variables::checkEnableSettings('staff-can-update-enquiry-source');

        Log::info('Enquiry updating for source', [
            'user_id' => $user->pk_int_user_id,
            'is_ops_staff' => $user->isOpsStaff(),
            'created_by' => $enquiry->created_by,
            'enquiry_id' => $enquiry->pk_int_enquiry_id,
            'source_id' => $enquiry->fk_int_enquiry_type_id,
            'request_source_id' => $request->source_id,
            'is_staff_update_source_allowed' => $isStaffUpdateSourceAllowed,
        ]);

        //staff should not change source
        if ($user->isOpsStaff()
            && $enquiry->created_by != $user->pk_int_user_id
            && $enquiry->fk_int_enquiry_type_id != $request->integer('source_id')
            && !$isStaffUpdateSourceAllowed) {

            Log::info('Skipping source update as Not Allowed update enquiry source - Form update', [
                'user_id' => $user->pk_int_user_id,
                'enquiry_id' => $enquiry->pk_int_enquiry_id,
                'source_id' => $request->integer('source_id'),
                'created_by' => $enquiry->created_by,
                'current_enquiry_type_id' => $enquiry->fk_int_enquiry_type_id,
            ]);
            return $this->response(200, true, 'Not Allowed update enquiry source', null);
        }

        try {
            DB::beginTransaction();
            if ($enquiry) {
                if ($request->enq_status_id == 15 && $this->vendorId == 880) {
                    DB::table('tbl_enquiries')->where('pk_int_enquiry_id', $request->id)
                        ->update(['next_follow_up' => NULL]);
                }
                if ($enquiry->fk_int_enquiry_type_id != $request->source_id) {
                    $assignSourceData = AutomationRule::where('trigger', 'source_change')->where('enquiry_source_id', $request->source_id)->where('vendor_id', $enquiry->fk_int_user_id)->where('action', 'assign')->orderby('id', 'DESC')->first();
                    if ($assignSourceData) {
                        AutomationRule::autoassign($assignSourceData, $enquiry->pk_int_enquiry_id);
                    }
                    $automation_rule_task = AutomationRule::where('trigger', 'source_change')->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)->where('vendor_id', $enquiry->fk_int_user_id)->where('action', 'task')->orderby('id', 'DESC')->first();
                    if ($automation_rule_task) {
                        if ($automation_rule_task->duration) {
                            $scheduled_date = Carbon::now()->addMinutes($automation_rule_task->duration);
                        } else {
                            $scheduled_date = Carbon::now();
                        }
                        $input = [
                            'name' => $automation_rule_task->task_title,
                            'description' => $automation_rule_task->task_description,
                            'scheduled_date' => $scheduled_date,
                            'task_category_id' => $automation_rule_task->task_category_id,
                            'assigned_to' => $automation_rule_task->task_assigned_to,
                            'assigned_by' => $enquiry->fk_int_user_id,
                            'vendor_id' => $enquiry->fk_int_user_id,
                            'enquiry_id' => $enquiry->pk_int_enquiry_id,
                            'status' => 0,
                        ];
                        Task::create($input);
                    }
                    $show = new Common();
                    $automation_rule_campaign = $show->getRule($this->vendorId, 'source_change', 'add_to_campaign', $enquiry->fk_int_enquiry_type_id);

                    if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                        if ($automation_rule_campaign->enquiry_source_id == $enquiry->fk_int_enquiry_type_id) {
                            try {
                                $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                                $show->addToCampaign($automation_rule_campaign, $enquiry, $this->userId, $this->vendorId, $cmp->type);

                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                            }
                        }
                    }
                }
                $enquiry = Enquiry::find($request->id);
                $old_id = $enquiry->staff_id;
                $updt = $enquiry->update(
                    [
                        'feedback_status' => $request->enq_status_id,
                        'vchr_enquiry_feedback' => $request->note,
                        'fk_int_user_id' => User::getVendorIdApi($request->user()->pk_int_user_id),
                        'fk_int_enquiry_type_id' => $request->source_id,
                        'vchr_customer_name' => $request->customer_name ?? '',
                        'date_of_birth' => $request->date_of_birth,
                        'vchr_customer_company_name' => $request->company_name,
                        'country_code' => $request->country_code,
                        'mobile_no' => $request->mobile_no,
                        'district_id' => $request->district_id,
                        'taluk_id' => $request->taluk_id,
                        'model_id' => $request->model_id,
                        'purchase_plan' => $request->purchase_plan,
                        'date_of_purchase' => $request->date_of_purchase,
                        'live_deal' => $request->live_deal,
                        'remarks' => $request->remarks,
                        'competing_model' => $request->competing_model_id,
                        'vchr_customer_mobile' => $request->country_code . $request->mobile_no,
                        'landline_number' => $request->landline_no,
                        'more_phone_numbers' => $request->more_phone_numbers,
                        'vchr_customer_email' => $request->email,
                        'purchase_date' => $request->purchase_date,
                        'exp_wt_grams' => $request->exp_wt_grams,
                        'address' => $request->address,
                        'function_date' => $request->function_date,
                        'fk_int_purpose_id' => $request->purpose_id,
                        'staff_id' => $request->staff_id,

                    ]);
                if ($request->staff_id) {
                    if ($request->staff_id != $old_id) {
                        $enquiry->assigned_date = Carbon::today();
                        $enquiry->save();
                        $staff = User::select('vchr_user_name', 'pk_int_user_id')->find($request->staff_id);
                        try {
                            $note = "Changed the agent to " . $staff->vchr_user_name;
                            event(new CreateFollowup($note, EnquiryFollowup::TYPE_ACTIVITY, $enquiry->pk_int_enquiry_id, $request->user()->pk_int_user_id));
                        } catch (\Exception $e) {
                            \Log::info($e->getMessage());
                        }
                    }
                }

                if ($updt) {
                    try {
                        event(new CreateFollowup("Lead details updated", EnquiryFollowup::TYPE_ACTIVITY, $request->id, $request->user()->pk_int_user_id));
                    } catch (\Exception $e) {
                        \Log::info($e->getMessage());
                    }
                }
                if (request()->has('lead_type_id')) {
                    if ($request->lead_type_id != '') {
                        Enquiry::where('pk_int_enquiry_id', $request->id)->update(['lead_type_id' => $request->lead_type_id]);
                    }
                }
                if ($request->lead_type_id) {
                    $lead_type = LeadType::where('id', $request->lead_type_id)->first();
                    if ($lead_type && $lead_type->name == 'Key Person') {
                        $exist = EnquiryType::where('vendor_id', $this->vendorId)->where('fk_int_enquiry_id', $enquiry->pk_int_enquiry_id)->first();
                        if (!$exist) {
                            $enquiry_source = new EnquiryType;
                            $enquiry_source->vendor_id = $this->vendorId;
                            $enquiry_source->vchr_enquiry_type = $request->customer_name;
                            $enquiry_source->fk_int_user_id = $this->vendorId;
                            $enquiry_source->fk_int_enquiry_id = $enquiry->pk_int_enquiry_id;
                            $enquiry_source->int_status = Variables::ACTIVE;
                            $enquiry_source->created_by = $request->user()->pk_int_user_id;
                            $enquiry_source->save();

                        }
                    }
                }
                if ($request->additional_field) {
                    $additional_flds = $request->additional_field;
                    if (is_array($additional_flds)) {
                        foreach ($additional_flds as $index => $value) {
                            if ($value != "") {
                                $field_name = LeadAdditionalField::find($index);
                                if ($field_name) {
                                    $eadditionalDetails = LeadAdditionalDetails::where([
                                        'enquiry_id' => $enquiry->pk_int_enquiry_id,
                                        'field_id' => $index
                                    ])->first();
                                    if (!$eadditionalDetails)
                                        $additionalDetails = new LeadAdditionalDetails();
                                    else
                                        $additionalDetails = LeadAdditionalDetails::find($eadditionalDetails->id);
                                    $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                                    $additionalDetails->field_id = $index;
                                    $additionalDetails->field_name = $field_name->field_name;
                                    if ($field_name->input_type == 8) {// Multi Select DropDown
                                        $additionalDetails->value = json_encode(explode(",", $value));
                                    } else {
                                        $additionalDetails->value = $value;
                                    }
                                    $additionalDetails->created_by = $this->userId;
                                    $additionalDetails->save();


                                    //  Code for marble gallary
                                    if ($field_name->field_name == MarbleGallery::LEAD_COMPLETE && $this->vendorId == MarbleGallery::MARBLE_GALLERY_ID)
                                        if ($value == 'Yes')
                                            $this->changeAttandanceStatusAndPushToPOS($enquiry, $this->roleId);
                                }

                            }

                        }
                    } else {
                        $fields = json_decode($additional_flds, TRUE);
                        foreach ($fields as $index => $value) {
                            if ($value != "") {
                                $id = str_replace("custom_field_", "", $index);
                                $field_name = LeadAdditionalField::find($id);
                                if ($field_name) {
                                    $eadditionalDetails = LeadAdditionalDetails::where([
                                        'enquiry_id' => $enquiry->pk_int_enquiry_id,
                                        'field_id' => $id
                                    ])->first();
                                    if (!$eadditionalDetails)
                                        $additionalDetails = new LeadAdditionalDetails();
                                    else
                                        $additionalDetails = LeadAdditionalDetails::find($eadditionalDetails->id);
                                    $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                                    $additionalDetails->field_id = $id;
                                    $additionalDetails->field_name = $field_name->field_name;
                                    if ($field_name->type_text == 'Image') {
                                        $filename = $value->store('public/custom_field_image');
                                        $additionalDetails->value = $filename;
                                    } elseif ($field_name->input_type == 8) {// Multi Select DropDown
                                        $additionalDetails->value = json_encode(explode(",", $value));
                                    } else {
                                        $additionalDetails->value = $value;
                                    }
                                    $additionalDetails->created_by = $this->userId;
                                    $additionalDetails->save();

                                    //  Code for marble gallary
                                    if ($field_name->field_name == MarbleGallery::LEAD_COMPLETE && $this->vendorId == MarbleGallery::MARBLE_GALLERY_ID)
                                        if ($value == 'Yes')
                                            $this->changeAttandanceStatusAndPushToPOS($enquiry, $this->roleId);
                                }

                            }

                        }
                    }
                } else {
                    $fields = LeadAdditionalField::where('vendor_id', $this->vendorId)->get();
                    foreach ($fields as $additional_field) {
                        if ($request['custom_field_' . $additional_field->id]) {
                            $eadditionalDetails = LeadAdditionalDetails::where([
                                'enquiry_id' => $enquiry->pk_int_enquiry_id,
                                'field_id' => $additional_field->id
                            ])->first();
                            if (!$eadditionalDetails)
                                $additionalDetails = new LeadAdditionalDetails();
                            else
                                $additionalDetails = LeadAdditionalDetails::find($eadditionalDetails->id);
                            $additionalDetails->enquiry_id = $enquiry->pk_int_enquiry_id;
                            $additionalDetails->field_id = $additional_field->id;
                            $additionalDetails->field_name = $additional_field->field_name;
                            if ($additional_field->type_text == 'Image') {
                                if ($request['custom_field_' . $additional_field->id]) {
                                    $filename = $request['custom_field_' . $additional_field->id]->store('public/custom_field_image');
                                    $additionalDetails->value = $filename;
                                }
                            } elseif ($additional_field->input_type == 8) {// Multi Select DropDown
                                $additionalDetails->value = json_encode(explode(",", $request['custom_field_' . $additional_field->id]));
                            } else {
                                $additionalDetails->value = $request['custom_field_' . $additional_field->id];
                            }
                            $additionalDetails->created_by = $this->userId;
                            $additionalDetails->save();
                        } else {
                            $eadditionalDetails = LeadAdditionalDetails::where([
                                'enquiry_id' => $enquiry->pk_int_enquiry_id,
                                'field_id' => $additional_field->id
                            ])->first();
                            if ($eadditionalDetails)
                                $additionalDetails = LeadAdditionalDetails::find($eadditionalDetails->id)->delete();
                        }
                    }
                }
            }

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->response(200, false, $exception->getMessage(), null);
        }

        return $this->response(200, false, 'success', null);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    /**
     * Remove Enquiry
     *
     * @authenticated
     *
     * @bodyParam id integer  required  Id. Example: 2
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function destroy(Request $request)
    {
        $validate_fields = [
            'id' => ['required', 'numeric']
        ];
        $validate_messages = [
            'id.required' => 'Enquiry Id Required',
            'id.numeric' => 'Enquiry Id should be numeric'
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        $enquiries = Enquiry::where('pk_int_enquiry_id', $request->id)->first();
        if ($enquiries) {
            $enquiries->delete();
            return $this->response(200, false, "Deleted", null);
        } else {
            return $this->response(200, true, "Enquiry Not Found", null);

        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponsea
     */
    /**
     * Dashboard
     *
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *          {
     *            "vchr_customer_mobile":999988777,
     *             "pk_int_enquiry_id":3
     *             "vchr_customer_name":"abcd",
     *             "vchr_purpose":"abcde",
     *              "vchr_status":"abc",
     *              "created_at": '1/2/2021',
     *          }
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public
    function getDashboardTasks(Request $request)
    {
        $enquiries = Enquiry::leftJoin('tbl_feedback_status', 'tbl_enquiries.feedback_status', '=', 'tbl_feedback_status.pk_int_feedback_status_id')
            ->leftJoin('tbl_enquiry_purpose', 'tbl_enquiries.fk_int_purpose_id', '=', 'tbl_enquiry_purpose.pk_int_purpose_id')
            ->whereIn('tbl_enquiries.read_status', [0, NULL])
            ->orderBy('tbl_enquiries.updated_at', 'DESC')
            //->where('staff_id', $request->user()->pk_int_user_id)
            ->where('tbl_enquiries.fk_int_user_id', $this->vendorId)
            // ->orwhere('tbl_enquiries.created_by', $request->user()->pk_int_user_id)
            ->select('tbl_enquiries.vchr_customer_mobile', 'pk_int_enquiry_id', \DB::raw('IFNULL(tbl_enquiries.vchr_customer_name,"No Customer Name") as vchr_customer_name'), \DB::raw('IFNULL(vchr_purpose,"None") as vchr_purpose'), 'tbl_feedback_status.vchr_status', 'tbl_enquiries.created_at');
        if ($this->roleId == User::STAFF)
            $enquiries = $enquiries->where('staff_id', $request->user()->pk_int_user_id)->paginate(15);
        else
            $enquiries = $enquiries->paginate(15);
        foreach ($enquiries as $enquiry) {
            $enquiry->created_date = Carbon::parse($enquiry->created_at)->format('F d');
        }
        //Start Update Last App Access Time and Version
        $user = User::select('last_app_used', 'pk_int_user_id', 'last_app_version')->find($request->user()->pk_int_user_id);
        $user->last_app_used = Carbon::now();
        if ($request->version)
            $user->last_app_version = $request->version;
        $user->save();
        //End Update Last App Access Time and Version

        return $this->response(200, false, null, $enquiries);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    /**
     * Get Calender Data
     *
     * @authenticated
     *
     * @bodyParam date date  required  Date. Example: 7/8/2000
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public
    function getCalendarData(Request $request)
    {
        $validate_fields = [
            'date' => ['required', 'date']
        ];
        $validate_messages = [
            'date.required' => 'Date Required',
            'date.date' => 'Please Enter valid date : yyyy-mm-dd'
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        $enquiries = Enquiry::leftJoin('tbl_enquiry_purpose', 'tbl_enquiries.fk_int_purpose_id', '=', 'tbl_enquiry_purpose.pk_int_purpose_id')
            ->whereDate('assigned_date', $request->date)
            // ->where('staff_id', $request->user()->pk_int_user_id)
            ->where('tbl_enquiries.fk_int_user_id', $this->vendorId)
            ->orderBy('tbl_enquiries.updated_at', 'DESC')
            ->select('pk_int_enquiry_id', 'tbl_enquiries.fk_int_user_id',
                'fk_int_purpose_id',
                'vchr_customer_mobile', 'vchr_enquiry_feedback', 'staff_id', 'assigned_date', \DB::raw('IFNULL(vchr_customer_name,"No Customer Name") as vchr_customer_name'), \DB::raw('IFNULL(tbl_enquiry_purpose.vchr_purpose,"None") as vchr_purpose'));
        if ($this->roleId == User::STAFF)
            $enquiries = $enquiries->where('staff_id', $request->user()->pk_int_user_id)->get();
        else
            $enquiries = $enquiries->get();
        return $this->response(200, false, "success", $enquiries);
    }

    /**
     * Update Status
     *
     * @authenticated
     *
     * @bodyParam enquiry_id integer  required  Enquiry Id. Example: 1
     * @bodyParam enq_status_id integer  required  Enquiry Status Id. Example: 1
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function updateStatus(Request $request)
    {
        $validate_fields = [
            'enquiry_id' => ['required'],
            'enq_status_id' => ['required']
        ];
        $validate_messages = [
            'enquiry_id.required' => 'Enquiry Id  Required',
            'enq_status_id.required' => 'Enquiry Status  Required',
        ];

        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        $enquiry = Enquiry::where('pk_int_enquiry_id', $request->enquiry_id)->first();
        if ($enquiry) {
            try {
                $old_status_id = $enquiry->feedback_status;
                $enquiry->feedback_status = $request->enq_status_id;
                $enquiry->save();
                Enquiry::statusChangeFunction($enquiry);
                // $enquiryfollowup = new EnquiryFollowup();
                // $enquiryfollowup->note = $request->enq_status_id;
                // $enquiryfollowup->log_type = EnquiryFollowup::TYPE_STATUS;
                // $enquiryfollowup->enquiry_id = $request->enquiry_id;
                // $enquiryfollowup->created_by = $this->userId;
                // $enquiryfollowup->save();

                try {
                    event(new CreateFollowup($request->enq_status_id, EnquiryFollowup::TYPE_STATUS, $request->enquiry_id, $this->userId, $old_status_id));
                } catch (\Exception $e) {
                    \Log::info($e->getMessage());
                }
                /** __________AUTOMATION_API_________**/
                $rule_api = $this->applicationObj->getRule($enquiry->fk_int_user_id, 'status_change', 'api', $request->enq_status_id);
                if ($rule_api && $rule_api->api != NULL) {
                    $status = FeedbackStatus::where('pk_int_feedback_status_id', $request->enq_status_id)
                        ->select('vchr_status')
                        ->first();
                    if ($status) {
                        $lead_status = $status->vchr_status;
                    } else {
                        $lead_status = "New Status";
                    }
                    $post_data = [
                        'customer_name' => $enquiry->vchr_customer_name,
                        'email' => $enquiry->vchr_customer_email,
                        'status' => $lead_status,
                        'phone' => $enquiry->vchr_customer_mobile,
                        'mobile' => $enquiry->mobile_no,
                        'flag' => "status_change",
                    ];
                    $this->applicationObj->postData($rule_api->api, $post_data);
                }
                /** __________AUTOMATION__API_________**/
                /** __________AUTOMATION_WEBHOOK_________**/
                $automation_rule = $this->applicationObj->getRule($enquiry->fk_int_user_id, 'status_change', 'webhook', '');
                if (count($automation_rule) > 0) {
                    foreach ($automation_rule as $w_hook) {
                        if ($w_hook->webhook_id != NULL) {
                            $webHook = WebHook::select('url')->where('id', $w_hook->webhook_id)->first();
                            if ($webHook) {
                                $this->applicationObj->postData($webHook->url, $post_data);
                            }
                        }
                    }
                }
                /** __________AUTOMATION_WEBHOOK_________**/


                /**--TASK---------------------------------------------**/
                $automation_rule_task = $this->applicationObj->getRule($enquiry->fk_int_user_id, 'status_change', 'task', $request->enq_status_id);
                if ($automation_rule_task) {
                    $input = [
                        'name' => $automation_rule_task->task_title,
                        'description' => $automation_rule_task->task_description,
                        'scheduled_date' => Carbon::tomorrow(),
                        'task_category_id' => $automation_rule_task->task_category_id,
                        'assigned_to' => $automation_rule_task->task_assigned_to,
                        'assigned_by' => $this->vendorId,
                        'vendor_id' => $this->vendorId,
                        'enquiry_id' => $enquiry->pk_int_enquiry_id,
                        'status' => 0,
                    ];
                    Task::create($input);
                }
                /**--TASK---------------------------------------------**/

                return $this->response(200, false, 'success', null);
            } catch (\Exception $e) {
                \Log::info($e->getMessage());
            }
        } else {
            return $this->response(200, false, "Not Found", null);
        }

    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Add Enquiry Note
     * Date : 14-11-2020 - Saturday
     */
    /**
     * Add Enquiry Notes
     *
     * @authenticated
     *
     * @bodyParam enquiry_id integer  required  Enquiry Id. Example: 2
     * @bodyParam note string  required  Note. Example: "abcds"
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function storeEnquiryNotes(Request $request)
    {
        Log::info('Store Enquiry Notes request from mobile app', ['request' => $request->all()]);

        $validate_fields = [
            'enquiry_id' => ['required'],
            'note' => ['required']
        ];
        $validate_messages = [
            'enquiry_id.required' => 'Enquiry Id  Required',
            'note.required' => 'Note Required',
        ];

        if (request()->has('type') && request('type') == 0) {
            return $this->response(200, true, "Something went wrong!", null);
        }

        if ($request->hasFile('note')) {
            /** added by goutham */
            if ($request->type && $request->type == EnquiryFollowup::TYPE_FILE_NOTE) {
                $validate_fields['note'] = 'mimes:jpg,jpeg,png,gif';
            } else if ($request->type == EnquiryFollowup::TYPE_DOCUMENT) {
                $validate_fields['note'] = 'mimes:pdf,docx,csv';
            } else {
                $validate_fields['note'] = 'mimes:m4a,mpga,mp3,wav,aac,mp4,awb';
            }
        }
        $file = $request->file('note');
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            if ($file->getClientOriginalExtension() != 'm4a')
                return $validated;
        }
        $enquiry = Enquiry::find($request->enquiry_id);
        if ($enquiry) {
            $enquiryfollowup = new EnquiryFollowup();
            if ($request->hasFile('note')) {
                $file = $request->file('note');
                $extension = $file->getClientOriginalExtension();
                $originalFilename = $file->getClientOriginalName();
                $name = explode('.', $originalFilename)[0];
                $slug = Str::slug($name);
                $originalName = Str::random(3) . substr(time(), 6, 8) . '-' . $slug . '.' . $extension;

                if ($request->type && $request->type == EnquiryFollowup::TYPE_FILE_NOTE) {
                    $path = 'file_notes/' . $this->vendorId . '/' . $originalName;
                    $filename = \Storage::disk('s3')->put($path, file_get_contents($file));
                    $enquiryfollowup->note = $path;
                    $enquiryfollowup->log_type = EnquiryFollowup::TYPE_FILE_NOTE;
                } /** added by goutham */ else if ($request->type && $request->type == EnquiryFollowup::TYPE_DOCUMENT) {
                    $path = 'document_notes/' . $this->vendorId . '/' . $originalName;
                    $filename = \Storage::disk('s3')->put($path, file_get_contents($file));
                    $enquiryfollowup->note = $path;
                    $enquiryfollowup->log_type = EnquiryFollowup::TYPE_DOCUMENT;
                } else {
                    $path = 'voice_notes/' . $this->vendorId . '/' . $originalName;
                    $filename = \Storage::disk('s3')->put($path, file_get_contents($file));
                    $enquiryfollowup->note = $path;
                    $enquiryfollowup->log_type = EnquiryFollowup::TYPE_VOICE_NOTE;
                }
            } else if (request('type') && request('type') == (string)EnquiryFollowup::TYPE_LOG_CALL) {
                $status = CallStatus::find(request('outcome'));
                $enquiryfollowup->note = request('note');
                $enquiryfollowup->log_type = request('type');
                $enquiryfollowup->response = $status ? $status->name : '';
                if (request('date') != " ") {
                    $date_time = request('date');
                    $enquiryfollowup->date = $date_time;
                }
            } else if (request('type') && request('type') == (string)EnquiryFollowup::TYPE_LOG_EMAIL) {
                $enquiryfollowup->note = request('note');
                $enquiryfollowup->log_type = request('type');
                $enquiryfollowup->response = request('outcome');
                if (request('date') != " ") {
                    $date_time = request('date');
                    $enquiryfollowup->date = $date_time;
                }
            } else if (request('type') && request('type') == (string)EnquiryFollowup::TYPE_LOG_MEETING) {
                $outcome = MeetingOutcome::find(request('outcome'));
                $enquiryfollowup->note = request('note');
                $enquiryfollowup->log_type = request('type');
                $enquiryfollowup->response = $outcome ? $outcome->name : '';;
                if (request('date') != " ") {
                    $date_time = request('date');
                    $enquiryfollowup->date = $date_time;
                }
            } else {
                $enquiryfollowup->response = request('outcome');
                if (request('date') != " ") {
                    $date_time = request('date');
                    $enquiryfollowup->date = $date_time;
                }
                $enquiryfollowup->note = request('note');
                $enquiryfollowup->log_type = EnquiryFollowup::TYPE_NOTE;
            }
            $enquiryfollowup->enquiry_id = $request->enquiry_id;
            $enquiryfollowup->created_by = $this->userId;
            $enquiryfollowup->save();

            $enquiry->lead_attension = 0;
            $enquiry->updated_at = now();
            $enquiry->update();

            return $this->response(200, false, 'success', null);
        } else {
            return $this->response(200, false, "Not Found", null);
        }
    }


    /**
     * Get Enquiry Notes
     *
     * @authenticated
     *
     * @bodyParam enquiry_id integer  required  Enquiry Id. Example:  34
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * Get Enquiry Notes
     */
    public function getEnquiryNotes(Request $request)
    {
        $validate_fields = [
            'enquiry_id' => ['required'],
        ];
        $validate_messages = [
            'enquiry_id.required' => 'Enquiry Id  Required',
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        if (Enquiry::where('pk_int_enquiry_id', $request->enquiry_id)->exists()) {
            $enquiryNotes = EnquiryFollowup::where('enquiry_id', $request->enquiry_id)->where(function ($where) use ($request) {
                if ($request->type && $request->type != 'all')
                    $where->where('log_type', $request->type);
                elseif (($request->type && $request->type != 'all') || !$request->type)
                    $where->where('log_type', EnquiryFollowup::TYPE_NOTE);
            })->select('id', 'enquiry_id', 'note', 'log_type', 'created_at')->whereIn('log_type', [1, 13, 14, 18])
                ->orderBy('id', 'DESC')
                ->get();

            if (count($enquiryNotes) > 0) {
                foreach ($enquiryNotes as $note) {
                    $note->created_time = Carbon::parse($note->created_at)->format('F d Y g:i A');
                }
                return $this->response(200, false, 'success', $enquiryNotes);
            } else {
                return $this->response(200, true, "Not Found", null);
            }
        } else {
            return $this->response(200, true, "Not Found", null);
        }
    }

    /**
     * Update Enquiry Notes
     *
     * @authenticated
     *
     * @bodyParam enquiry_id integer required Enquiry Id. Example: 78
     * @bodyParam note string required Note. Example: abcd
     * @bodyParam id integer required Id. Example: 34
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateEnquiryNotes(Request $request)
    {
        $validate_fields = [
            'enquiry_id' => ['required'],
            'note' => ['required'],
            'id' => ['required']
        ];
        $validate_messages = [
            'enquiry_id.required' => 'Enquiry Id  Required',
            'note.required' => 'Note Required',
            'id.required' => 'Note Required',
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        $enquiryfollowup = EnquiryFollowup::where('id', $request->id)->where('enquiry_id', $request->enquiry_id)
            ->first();
        if ($enquiryfollowup) {
            $enquiryfollowup->note = $request->note;
            $enquiryfollowup->created_by = $this->userId;
            $enquiryfollowup->save();
            return $this->response(200, false, 'success', null);
        } else {
            return $this->response(200, true, "Not Found", null);
        }
    }

    /**
     * Delete Enquiry Notes
     *
     * @authenticated
     *
     * @bodyParam enquiry_id integer required Enquiry Id. Example: 78
     * @bodyParam id integer required Id. Example: 34
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteEnquiryNotes(Request $request)
    {
        $validate_fields = [
            'enquiry_id' => ['required'],
            'id' => ['required']
        ];
        $validate_messages = [
            'enquiry_id.required' => 'Enquiry Id  Required',
            'id.required' => 'Note Required',
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        $enquiryfollowup = EnquiryFollowup::where('id', $request->id)->where('enquiry_id', $request->enquiry_id)
            ->where('created_by', $this->userId)->first();
        if ($enquiryfollowup) {
            EnquiryFollowup::where('id', $request->id)->where('enquiry_id', $request->enquiry_id)
                ->where('created_by', $this->userId)->delete();
            return $this->response(200, false, 'success', null);
        } else {
            return $this->response(200, true, "Not Found", null);
        }
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * November 15 2020
     */
    /**
     * SMS Templates
     *
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *      {
     *          "pk_int_sms_template_id": 1,
     *          "vchr_sms_template_title":"abc",
     *          "sms_template_code": 2,
     *          "fk_int_user_id":  6
     *      }
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function smsTemplates()
    {
        $sms_templates = SmsTemplateCode::
        where('fk_int_user_id', $this->vendorId)
            ->select('pk_int_sms_template_id', 'vchr_sms_template_title', 'sms_template_code', 'fk_int_user_id')
            ->orderBy('pk_int_sms_template_id', 'DESC')
            ->get();

        if (count($sms_templates) > 0) {
            return $this->response(200, false, 'success', $sms_templates);
        } else {
            return $this->response(200, true, 'No Templates added', null);
        }

    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * November 15 2020
     */
    /**
     * whatsAppTemplates
     *
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *      {
     *          "pk_int_whatsapp_template_id": 1,
     *          "vchr_whatsapp_template_title":"abc",
     *          "text_whatsapp_template_description": "abcd",
     *          "fk_int_user_id":  6
     *      }
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function whatsAppTemplates()
    {
        $w_templates = WhatsappTemplate::where('fk_int_user_id', $this->vendorId)
            ->select('pk_int_whatsapp_template_id', 'vchr_whatsapp_template_title', 'text_whatsapp_template_description', 'fk_int_user_id')
            ->where('int_status', 1)
            ->orderBy('pk_int_whatsapp_template_id', 'DESC')
            ->get();

        if (count($w_templates) > 0) {
            return $this->response(200, false, 'success', $w_templates);
        } else {
            return $this->response(200, true, 'No Templates added', null);
        }
    }

    /**
     * Lead Types
     *
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function leadTypes()
    {
        if (Cache::get('lead_types' . $this->vendorId)) {
            $data = Cache::get('lead_types' . $this->vendorId);
        } else {
            $data = LeadType::where('vendor_id', $this->vendorId)
                ->orderBy('id', 'DESC')
                ->get();

            Cache::put('lead_types' . $this->vendorId, $data, null, true);
        }

        if (count($data) > 0) {
            return $this->response(200, false, 'success', $data);
        } else {
            return $this->response(200, true, 'Data Not Found', null);
        }
    }

    /**
     * Update Enquiry Fields
     *
     * @authenticated
     *
     * @bodyParam enquiry_id  integer  required  Enquiry Id. Example: 10
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function updateEnquiryFields(Request $request)
    {
        /**
         * @var User $user
         */
        $user = $request->user();

        Log::info('Updating enquiry fields from mobile app', ['request' => $request->all(), 'user_id' => $user->pk_int_user_id]);

        $validate_fields = [
            'enquiry_id' => ['required']
        ];
        $validate_messages = [
            'enquiry_id.required' => 'Enquiry Id  Required'
        ];

        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        try {
            $enquiry = Enquiry::query()->where('pk_int_enquiry_id', $request->enquiry_id)->first();
            if ($enquiry) {

                //staff should not change source
                if ($request->has('source_id')
                    && $user->isOpsStaff()
                    && $enquiry->created_by != $user->pk_int_user_id
                    && $enquiry->fk_int_enquiry_type_id != $request->integer('source_id')
                    && !Variables::checkEnableSettings('staff-can-update-enquiry-source')
                ) {

                    Log::info('Skipping source update as Not Allowed update enquiry source', [
                        'user_id' => $user->pk_int_user_id,
                        'enquiry_id' => $enquiry->pk_int_enquiry_id,
                        'source_id' => $request->integer('source_id'),
                        'created_by' => $enquiry->created_by,
                        'current_enquiry_type_id' => $enquiry->fk_int_enquiry_type_id,
                        'ops_staff' => $user->isOpsStaff(),
                    ]);
                    return $this->response(200, true, 'Not Allowed update enquiry source', null);
                }

                if ($request->district_id) {
                    $old_district_id = $enquiry->district_id;
                    $enquiry->district_id = $request->district_id;
                } else {
                    $enquiry->district_id = $enquiry->district_id;
                }

                if ($request->taluk_id) {
                    $old_taluk_id = $enquiry->taluk_id;
                    $enquiry->taluk_id = $request->taluk_id;
                } else {
                    $enquiry->taluk_id = $enquiry->taluk_id;
                }

                if ($request->purchase_date) {
                    $old_purchase_date = $enquiry->purchase_date;
                    $enquiry->purchase_date = $request->purchase_date;
                }

                // model change
                if ($request->model_id) {
                    $old_model_id = $enquiry->model_id;
                    $enquiry->model_id = $request->model_id;
                }

                // purchase plan
                if ($request->purchase_plan) {
                    $old_purchase_plan = $enquiry->purchase_plan;
                    $enquiry->purchase_plan = $request->purchase_plan;
                }

                // live deal
                if ($request->live_deal) {
                    $old_live_deal = $enquiry->live_deal;
                    $enquiry->live_deal = $request->live_deal;
                }

                // remarks
                if ($request->remarks) {
                    $old_remarks = $enquiry->remarks;
                    $enquiry->remarks = $request->remarks;
                }

                // competing_model
                if ($request->competing_model_id) {
                    $old_competing_model = $enquiry->competing_model;
                    $enquiry->competing_model = $request->competing_model_id;
                }

                // date of purchase
                if ($request->date_of_purchase) {
                    $old_date_of_purchase = $enquiry->date_of_purchase;
                    $enquiry->date_of_purchase = $request->date_of_purchase;
                }

                if ($request->exp_wt_grams) {
                    $old_exp_wt_grams = $enquiry->exp_wt_grams;
                    $enquiry->exp_wt_grams = $request->exp_wt_grams;
                }
                if ($request->lead_type_id) {
                    $old_lead_type_id = $enquiry->lead_type_id;
                    $enquiry->lead_type_id = $request->lead_type_id;
                }
                //Source
                if ($request->source_id) {
                    $old_source_id = $enquiry->fk_int_enquiry_type_id;
                    $enquiry->fk_int_enquiry_type_id = $request->source_id;
                }
                //Purpose
                if ($request->purpose_id) {
                    $old_purpose_id = $enquiry->fk_int_purpose_id;
                    $enquiry->fk_int_purpose_id = $request->purpose_id;
                }
                //Status
                if ($request->feedback_status_id) {
                    $old_feedback_status_id = $enquiry->feedback_status;
                    $enquiry->feedback_status = $request->feedback_status_id;

                    if ($request->feedback_status_id == 15 && $this->vendorId == 880) {
                        DB::table('tbl_enquiries')->where('pk_int_enquiry_id', $request->enquiry_id)
                            ->update(['next_follow_up' => NULL]);
                    }
                }
                //Agent
                if ($request->staff_id) {
                    $old_staff_id = $enquiry->staff_id;
                    if ($old_staff_id != $request->staff_id) {
                        $enquiry->assigned_date = Carbon::today();
                    }
                    $enquiry->staff_id = $request->staff_id;
                }
                //Function Date
                if ($request->function_date) {
                    $old_function_date = $enquiry->function_date;
                    $enquiry->function_date = $request->function_date;
                }
                //Address
                if ($request->address) {
                    $old_address = $enquiry->address;
                    $enquiry->address = $request->address;
                }
                //Comment
                if ($request->note) {
                    $old_note = $enquiry->vchr_enquiry_feedback;
                    $enquiry->vchr_enquiry_feedback = $request->note;
                }
                //Landline number
                if ($request->landline_no) {
                    $old_landline_no = $enquiry->landline_number;
                    $enquiry->landline_number = $request->landline_no;
                }
                //More Phone Numbers
                if ($request->more_phone_numbers) {
                    $old_more_phone_numbers = $enquiry->more_phone_numbers;
                    $enquiry->more_phone_numbers = $request->more_phone_numbers;
                }
                //
                $enquiry->save();

                $enquiryfollowup = new EnquiryFollowup();
                $note = [];
                $logType = EnquiryFollowup::TYPE_ACTIVITY;

                // for model change
                if ($request->model_id && $request->model_id != $old_model_id) {
                    $old_model = TataModel::where('id', $old_model_id)->first();
                    $model = TataModel::where('id', $request->model_id)->first();
                    if ($model) {
                        array_push($note, 'Model changed from ' . ($old_model ? $old_model->name : 'NA') . ' to ' . $model->name);
                    } else {
                        array_push($note, 'Model changed');
                    }
                }

                // for competing model
                if ($request->competing_model_id && $request->competing_model_id != $old_competing_model) {
                    $old_competing_model = TataCompetingModel::where('id', $old_competing_model)->first();
                    $competing_model = TataCompetingModel::where('id', $request->competing_model_id)->first();
                    if ($competing_model) {
                        array_push($note, 'Competing model changed from ' . ($old_competing_model ? $old_competing_model->name : 'NA') . ' to ' . $competing_model->name);
                    } else {
                        array_push($note, 'Competing model changed');
                    }
                }

                // for remarks
                if ($request->remarks && $request->remarks != $old_remarks) {
                    array_push($note, 'Remarks changed');
                }

                // for live deal
                if ($request->live_deal && $request->live_deal != $old_live_deal) {
                    array_push($note, 'Live ' . getDealName(true) . ' changed from ' . $old_live_deal . ' to ' . $request->live_deal);
                }

                // for date of purchase
                if ($request->date_of_purchase && $request->date_of_purchase != $old_date_of_purchase) {
                    array_push($note, 'Date of purchase changed from ' . $old_date_of_purchase . ' to ' . $request->date_of_purchase);
                }

                // for purchase plan
                if ($request->purchase_plan && $request->purchase_plan != $old_purchase_plan) {
                    array_push($note, 'Purchase plan changed from ' . $old_purchase_plan . ' to ' . $request->purchase_plan);
                }

                // district
                if ($request->district_id && $request->district_id != $old_district_id) {
                    $old_district = District::where('id', $old_district_id)->first();
                    $district = District::where('id', $request->district_id)->first();
                    if ($district) {
                        array_push($note, 'District changed from ' . ($old_district ? $old_district->name : 'NA') . ' to ' . $district->name);
                    } else {
                        array_push($note, 'District changed');
                    }
                }

                // taluk
                if ($request->taluk_id && $request->taluk_id != $old_taluk_id) {
                    $old_taluk = Taluk::where('id', $old_taluk_id)->first();
                    $taluk = Taluk::where('id', $request->taluk_id)->first();
                    if ($taluk) {
                        array_push($note, 'Taluk changed from ' . ($old_taluk ? $old_taluk->name : 'NA') . ' to ' . $taluk->name);
                    } else {
                        array_push($note, 'Taluk changed');
                    }
                }

                if ($request->purchase_date && $request->purchase_date != $old_purchase_date)
                    array_push($note, 'Purchase Date Changed from ' . $old_purchase_date . ' to ' . $request->purchase_date);
                if ($request->exp_wt_grams && $request->exp_wt_grams != $old_exp_wt_grams)
                    array_push($note, 'Expected Weight in Grams Changed from ' . $old_exp_wt_grams . ' to ' . $request->exp_wt_grams);
                if ($request->lead_type_id && $request->lead_type_id != $old_lead_type_id) {
                    $old_lead_type = LeadType::where('id', $old_lead_type_id)->first();
                    $lead_type = LeadType::where('id', $request->lead_type_id)->first();
                    if ($lead_type)
                        array_push($note, 'Lead Type Changed from ' . ($old_lead_type ? $old_lead_type->name : 'NA') . ' to ' . $lead_type->name);
                    else
                        array_push($note, 'Lead Type Changed');
                }
                //Source
                if ($request->source_id && ($request->source_id != $old_source_id)) {

                    $old_source = EnquiryType::where('pk_int_enquiry_type_id', $old_source_id)->first();
                    $source = EnquiryType::where('pk_int_enquiry_type_id', $request->source_id)->first();
                    if ($source)
                        array_push($note, 'Source Changed from ' . ($old_source ? $old_source->vchr_enquiry_type : 'NA') . ' to ' . $source->vchr_enquiry_type);
                    else
                        array_push($note, 'Source Changed');

                    $assignSourceData = AutomationRule::query()->where('trigger', 'source_change')->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)->where('vendor_id', $enquiry->fk_int_user_id)->where('action', 'assign')->orderby('id', 'DESC')->first();
                    if ($assignSourceData) {
                        AutomationRule::autoassign($assignSourceData, $enquiry->pk_int_enquiry_id);
                    }
                    $automation_rule_task = AutomationRule::query()->where('trigger', 'source_change')->where('enquiry_source_id', $enquiry->fk_int_enquiry_type_id)->where('vendor_id', $enquiry->fk_int_user_id)->where('action', 'task')->orderby('id', 'DESC')->first();
                    if ($automation_rule_task) {
                        if ($automation_rule_task->duration) {
                            $scheduled_date = Carbon::now()->addMinutes($automation_rule_task->duration);
                        } else {
                            $scheduled_date = Carbon::now();
                        }
                        $input = [
                            'name' => $automation_rule_task->task_title,
                            'description' => $automation_rule_task->task_description,
                            'scheduled_date' => $scheduled_date,
                            'task_category_id' => $automation_rule_task->task_category_id,
                            'assigned_to' => $automation_rule_task->task_assigned_to,
                            'assigned_by' => $enquiry->fk_int_user_id,
                            'vendor_id' => $enquiry->fk_int_user_id,
                            'enquiry_id' => $enquiry->pk_int_enquiry_id,
                            'status' => 0,
                        ];
                        Task::create($input);
                    }

                    $show = new Common();
                    $automation_rule_campaign = $show->getRule($this->vendorId, 'source_change', 'add_to_campaign', $enquiry->fk_int_enquiry_type_id);

                    if ($automation_rule_campaign && $automation_rule_campaign->campaign_id != null) {
                        if ($automation_rule_campaign->enquiry_source_id == $enquiry->fk_int_enquiry_type_id) {
                            try {
                                $cmp = LeadCampaign::find($automation_rule_campaign->campaign_id);
                                $show->addToCampaign($automation_rule_campaign, $enquiry, $this->userId, $this->vendorId, $cmp->type);

                            } catch (\Exception $e) {
                                \Log::info($e->getMessage());
                            }
                        }
                    }
                }
                //Purpose
                if ($request->purpose_id && $request->purpose_id != $old_purpose_id) {
                    $old_purpose = EnquiryPurpose::where('pk_int_purpose_id', $old_purpose_id)->first();
                    $purpose = EnquiryPurpose::where('pk_int_purpose_id', $request->purpose_id)->first();
                    $logType = EnquiryFollowup::ENQ_PURPOSE;
                    if ($purpose) {
                        // array_push($note, 'Purpose Changed from ' . ($old_purpose ? $old_purpose->vchr_purpose : 'NA') . ' to ' . $purpose->vchr_purpose);
                        array_push($note, $request->purpose_id);
                    } else {
                        array_push($note, 'Purpose Changed');
                    }
                }
                //Status
                if ($request->feedback_status_id && ($request->feedback_status_id != $old_feedback_status_id)) {

                    $enquiryfollowup->old_status_id = $old_feedback_status_id;
                    $old_feedback_status = FeedbackStatus::where('pk_int_feedback_status_id', $old_feedback_status_id)->first();
                    $feedback_status = FeedbackStatus::where('pk_int_feedback_status_id', $request->feedback_status_id)->first();
                    $logType = EnquiryFollowup::TYPE_STATUS;
                    if ($feedback_status) {
                        // array_push($note, 'Status Changed from ' . ($old_feedback_status ? $old_feedback_status->vchr_status : 'NA') . ' to ' . $feedback_status->vchr_status);
                        array_push($note, $request->feedback_status_id);
                    } else {
                        array_push($note, 'Status Changed');
                    }

                    Enquiry::statusChangeFunction($enquiry);

                    $automation_rule_email = AutomationRule::where('vendor_id', $this->vendorId)
                        ->where('trigger', 'status_change')
                        ->where('action', 'email')
                        ->where('feedback_status_id', $request->feedback_status_id)
                        ->orderBy('id', 'DESC')
                        ->first();
                    if ($automation_rule_email) {
                        AutomationRule::sendMail($request->enquiry_id, $automation_rule_email);
                    }

                    // Automation rule status
                    $automation_rule_statu_task = AutomationRule::where('trigger', 'status_change')
                        ->where('feedback_status_id', $request->feedback_status_id)
                        ->where('vendor_id', $enquiry->fk_int_user_id)
                        ->where('action', 'task')
                        ->orderby('id', 'DESC')
                        ->first();
                    if ($automation_rule_statu_task) {
                        if ($automation_rule_statu_task->duration) {
                            $scheduled_date = Carbon::now()->addMinutes($automation_rule_statu_task->duration);
                        } else {
                            $scheduled_date = Carbon::now();
                        }
                        $input = [
                            'name' => $automation_rule_statu_task->task_title,
                            'description' => $automation_rule_statu_task->task_description,
                            'scheduled_date' => $scheduled_date,
                            'task_category_id' => $automation_rule_statu_task->task_category_id,
                            'assigned_to' => $automation_rule_statu_task->task_assigned_to,
                            'assigned_by' => $enquiry->fk_int_user_id,
                            'vendor_id' => $enquiry->fk_int_user_id,
                            'enquiry_id' => $enquiry->pk_int_enquiry_id,
                            'status' => 0,
                        ];
                        Task::create($input);
                    }

                    // Status change push webhook data
                    $commonObj = new Common;
                    $automation_rule = $commonObj->getRule($this->vendorId, 'status_change', 'webhook');
                    $status = FeedbackStatus::where('pk_int_feedback_status_id', $request->feedback_status_id)
                        ->select('vchr_status')
                        ->first();
                    $post_data = [
                        'customer_name' => $enquiry->vchr_customer_name,
                        'email' => $enquiry->vchr_customer_email,
                        'status' => $status->vchr_status ?? null,
                        'phone' => $enquiry->vchr_customer_mobile,
                        'mobile' => $enquiry->mobile_no,
                        'flag' => "status_change",
                        'state' => ($this->vendorId == 1119) ? $enquiry->additional_details->where('field_id', '=', 317 /* state id is for bazani */)->value('value') ?? null : null
                    ];
                    if (count($automation_rule) > 0) {
                        foreach ($automation_rule as $w_hook) {
                            if ($w_hook->webhook_id != null && $w_hook->feedback_status_id == $request->feedback_status_id) {
                                try {
                                    $webHook = $commonObj->getWebHookById($w_hook->webhook_id);
                                } catch (\Exception $th) {
                                    \Log::info($th->getMessage());
                                }
                                if ($webHook) {
                                    try {
                                        try {
                                            $commonObj->postToWebHook($webHook->url, $post_data);
                                        } catch (\Exception $e) {
                                            \Log::info($e->getMessage());
                                        }
                                    } catch (\Exception $e) {
                                        \Log::info($e->getMessage());
                                    }
                                }
                            }
                        }
                    }

                }
                //source chnage


                //Agent
                if ($request->staff_id && ($request->staff_id != $old_staff_id)) {
                    $old_staff = User::select('vchr_user_name')->find($old_staff_id);
                    $staff = User::select('vchr_user_name')->find($enquiry->staff_id);
                    if ($staff)
                        array_push($note, 'Agent Changed from ' . ($old_staff ? $old_staff->vchr_user_name : 'NA') . ' to ' . $staff->vchr_user_name);
                    else
                        array_push($note, 'Agent Changed');
                    event(new LeadAssigned($enquiry->pk_int_enquiry_id, $this->userId));
                }
                //Function Date
                if ($request->function_date && $request->function_date != $old_function_date)
                    array_push($note, 'Function Date Changed from ' . $old_function_date . ' to ' . $request->function_date);
                //Address
                if ($request->address && $request->address != $old_address)
                    array_push($note, 'Address Changed from ' . $old_address . ' to ' . $request->address);
                $enquiry->vchr_enquiry_feedback = $request->note;
                //Comment
                if ($request->note && $request->note != $old_note)
                    array_push($note, 'Comment Changed from ' . $old_note . ' to ' . $request->note);
                //Landline Number
                if ($request->landline_no && $request->landline_no != $old_landline_no)
                    array_push($note, 'Landline Number Changed from ' . $old_landline_no . ' to ' . $request->landline_no);
                //More Phone Number
                if ($request->more_phone_numbers && $request->more_phone_numbers != $old_more_phone_numbers)
                    array_push($note, 'Alternate Number Changed from ' . $old_more_phone_numbers . ' to ' . $request->more_phone_numbers);

                //Change additional field
                try {
                    if ($request->additional_key && $request->additional_value) {
                        $fieldName = $this->updateAdditionalField($request);
                        array_push($note, $fieldName . ' Field updated');
                        $enquiryfollowup->log_type = EnquiryFollowup::TYPE_ACTIVITY;
                    }
                } catch (\Exception $e) {

                }

                $enquiryfollowup->note = implode(',', $note);
                $enquiryfollowup->log_type = $logType;
                $enquiryfollowup->enquiry_id = $request->enquiry_id;
                $enquiryfollowup->created_by = $this->userId;
                $enquiryfollowup->save();
                if ($request->lead_type_id) {
                    $enquiry = Enquiry::where('pk_int_enquiry_id', $request->enquiry_id)->first();
                    if ($enquiry) {
                        $lead_type = LeadType::where('id', $request->lead_type_id)->first();
                        if ($lead_type && $lead_type->name == 'Key Person') {
                            $exist = EnquiryType::where('vendor_id', $this->vendorId)->where('fk_int_enquiry_id', $enquiry->pk_int_enquiry_id)->first();
                            if (!$exist) {
                                $enquiry_source = new EnquiryType;
                                $enquiry_source->vendor_id = $this->vendorId;
                                $enquiry_source->vchr_enquiry_type = $enquiry->vchr_customer_name;
                                $enquiry_source->fk_int_user_id = $this->vendorId;
                                $enquiry_source->fk_int_enquiry_id = $enquiry->pk_int_enquiry_id;
                                $enquiry_source->int_status = Variables::ACTIVE;
                                $enquiry_source->created_by = $request->user()->pk_int_user_id;
                                $enquiry_source->save();

                            }
                        }
                    }
                }
                return $this->response(200, false, 'success', null);
            } else {
                return $this->response(200, false, "Not Found", null);
            }
        } catch (\Exception $e) {
            \Log::info($e->getMessage());
        }

    }

    /**
     * Function to add schedule for next follow up
     *
     * @param Request $request
     * @return Json
     */
    public function addScheduleForNextFollowUp(Request $request)
    {
        $validate_fields = [
            'id' => ['required'],
            'time' => ['required'],
            'date' => ['required', 'date']
        ];

        $validate_messages = [
            'id.required' => 'Enquiry Id  Required',
            'time.required' => 'Time Required',
            'date.required' => 'Date Required',
        ];

        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {

            return $validated;
        }

        if (Enquiry::where('pk_int_enquiry_id', $request->id)->exists()) {

            DB::table('tbl_enquiries')->where('pk_int_enquiry_id', $request->id)
                ->update(['next_follow_up' => $request->date . ' ' . $request->time]);

            $data = $request->all();
            $data['name'] = Auth::user()->vchr_user_name;
            $data['duration'] = '';
            $data['note'] = $request->note ?? 'Follow up';
            $data['id'] = $request->id;
            $data['date'] = $request->date;
            $data['time'] = $request->time;

            EnquiryFollowup::addSchedule($data, $this->userId);

            return $this->response(200, false, 'success', null);
        } else {

            return $this->response(200, false, "Not Found", null);
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    /**
     * Enquiry Search
     * @bodyParam search_key string  required   Example: customer name/mobile number/email/enquiry purpose
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *           {
     *             "vchr_customer_mobile":98876676,
     *              "pk_int_enquiry_id":76,
     *              "vchr_customer_name":"abcd",
     *              "vchr_purpose":"abcd",
     *              "vchr_status":"abcd",
     *              "created_at":8/9/2020
     *           }
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */

    public function enquirySearch(Request $request)
    {
        Log::info('Searching for enquiry from mobile app-legacy', ['request' => $request->all()]);

        $validation = Validator::make($request->all(), [
            'search_key' => ['required'],
        ]);

        if ($validation->fails()) {
            return $this->response(400, true, null, $validation->errors());
        }


        /** @var User $user */
        $user = Auth::user();

        $enquiriesQuery = Enquiry::query()
            ->where('tbl_enquiries.fk_int_user_id', $user->getBusinessId())
            ->select(
                'tbl_enquiries.staff_id',
                'tbl_enquiries.vchr_customer_mobile',
                'tbl_enquiries.vchr_customer_company_name as company_name',
                'pk_int_enquiry_id',
                'tbl_enquiries.fk_int_user_id',
                DB::raw('IFNULL(tbl_enquiries.vchr_customer_name, "No Customer Name") as vchr_customer_name'),
                'tbl_enquiries.created_at'
            )
            ->addSelect([
                'vchr_purpose' => EnquiryPurpose::query()
                    ->selectRaw('coalesce(`tbl_enquiry_purpose`.`vchr_purpose`, "None")')
                    ->whereColumn('tbl_enquiry_purpose.pk_int_purpose_id', '=', 'tbl_enquiries.fk_int_purpose_id')
                    ->limit(1),
            ])
            ->with([
                'assigned_user' => function ($q) {
                    $q->select('pk_int_user_id', 'vchr_user_name');
                }])
            ->orderByDesc('tbl_enquiries.updated_at');

        if (Variables::checkEnableSettings('global-search') || $user->isOpsUser()) {
            $enquiriesQuery->addSelect(DB::raw('1 as is_own_lead'));
        } else {
            $assignedStaffIds = [
                $this->userId,
                ...AgentStaff::query()->where('agent_id', $user->pk_int_user_id)->pluck('staff_id')->toArray()
            ];

            $enquiriesQuery->addSelect([
                DB::raw('(CASE WHEN tbl_enquiries.staff_id IN (' . implode(',', $assignedStaffIds) . ') OR tbl_enquiries.created_by IN (' . implode(',', $assignedStaffIds) . ') THEN 1 ELSE 0 END) AS is_own_lead'),
                DB::raw('(CASE WHEN tbl_enquiries.staff_id = ' . $user->pk_int_user_id . ' OR tbl_enquiries.created_by = ' . $user->pk_int_user_id . ' THEN tbl_enquiries.vchr_customer_mobile ELSE CONCAT(REPEAT("*", LENGTH(tbl_enquiries.vchr_customer_mobile) - 4), RIGHT(tbl_enquiries.vchr_customer_mobile, 4)) END) AS vchr_customer_mobile')
            ]);
        }

        if ($request->has('search_key') && $request->filled('search_key')) {
            $searchKey = $request->get('search_key');
            $enquiriesQuery = $enquiriesQuery
                ->where(function ($query) use ($searchKey) {
                    $query->searchLegacy($searchKey);
                });
        }

        $enquiries = $enquiriesQuery->paginate(10);

        $data['numberMasking'] = Variables::checkEnableSettings('number-masking');
        $data['total_count'] = $enquiries->total();
        $data['unread_count'] = 0;
        $data['enquires'] = $enquiries;

        return $this->response(200, false, null, $data);

    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    /**
     * Enquiry Check
     * @bodyParam mobile string  required   Example: customer mobile number
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */

    public function enquiryCheck(Request $request)
    {

        $validate_fields = [
            'mobile' => ['required'],
        ];
        $validate_messages = [
            'mobile.required' => 'Mobile Number is  Required',
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        $enquiry = Enquiry::where('tbl_enquiries.fk_int_user_id', $this->vendorId)
            ->where('mobile_no', $request->mobile)->first();
        return $this->response(200, $enquiry ? true : false, $enquiry ? "Lead already exist" : null);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    /**
     * Click to Call
     * @bodyParam enquiry_id number  required   Example: 1
     * @authenticated
     *
     *
     * @response {
     *  "error": true,
     *  "data" : [
     *           {
     *             "phone_number":98876676
     *           }
     *  ],
     *  "message": "OK",
     *  "time" : 545454654674
     * }
     *
     */
    public function clickToCallBridging(Request $request)
    {
        Log::info('Request received for click to call from mobile app', ['request' => $request->all()]);

        $settings = CloudTelephonySetting::query()
            ->where('vendor_id', $this->vendorId)
            ->where('status', 1)
            ->where('default', 1)
            ->first();
        if ($settings) {
            if ($settings->operator == "2") {
                try {
                    $callBonvoice = new BonvoiceController();
                    return $callBonvoice->clickToCallBonvoice($request, $this->user);
                } catch (\Exception $e) {
                }

            } elseif ($settings->operator == "3") {
                $callVoxbay = new VoxbayController();
                return $callVoxbay->clickToCallVoxbayX($request, auth()->user());

            } else {
                $callVoxbay = new VoxbayController();
                return $callVoxbay->clickToCallVoxbay($request, $this->user);
            }
        } else {
            $callVoxbay = new VoxbayController();
            return $callVoxbay->clickToCallVoxbay($request, $this->user);
        }

    }

    public function clickToCall(Request $request)
    {
        $validate_fields = [
            'enquiry_id' => ['required'],
        ];
        $validate_messages = [
            'enquiry_id.required' => 'Enquiry ID is Required',
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }
        $enquiry = Enquiry::find($request->enquiry_id);
        $data = [];
        if ($enquiry) {
            $group = GroupUsers::where([
                'fk_int_user_id' => $enquiry->fk_int_user_id,
            ])->where(function ($where) use ($enquiry) {
                $where->where('int_mobile', $enquiry->mobile_no)->orWhere('enquiry_id', $enquiry->pk_int_enquiry_id);
            })->first();
            $user_id = $this->roleId == User::STAFF ? $request->user()->parent_user_id : $request->user()->pk_int_user_id;
            if ($group && $group->ivr) {
                $ext = true;
                $ivr_number = $group->ivr;
            } else {
                $ext = false;
                $ivr_number = $enquiry->did_number;
            }
            if ($ivr_number) {
                if ($enquiry->fk_int_user_id == 1346) {
                    $url = 'http://61.2.140.67:8080/api/call.php?uid=' . $ivr_number->uid . '&pin=' . $ivr_number->pin . '&agent=' . $request->user()->extension . '&destination=' . ($request->phone_number ?? $enquiry->vchr_customer_mobile) . '&callForward=' . $request->user()->call_forwarded;
                } else {
                    // if ($ext) {
                    $extension = IvrExtension::where('virtual_number_id', $ivr_number->pk_int_virtual_number_id)->where('staff_id', $request->user()->pk_int_user_id)->first();
                    $agent_number = User::select('mobile')->find($request->user()->pk_int_user_id);
                    if ($agent_number) {
                        $agent_number = $agent_number->mobile;
                    } else {
                        $agent_number = null;
                    }
                    $extension_number = $extension ? $extension->extension : $ivr_number->extension;
                    $url = 'https://pbx.voxbaysolutions.com/api/clicktocall.php?uid=' . $ivr_number->uid . '&pin=' . $ivr_number->pin . '&source=' . $request->user()->vchr_user_mobile . '&destination=' . ($request->phone_number ?? $enquiry->vchr_customer_mobile) . '&ext=' . $extension_number . '&callerid=' . $ivr_number->vchr_virtual_number . '&agent=' . $agent_number;
                    // $url = 'https://pbx.voxbaysolutions.com/api/clicktocall.php?uid=HJBKNgttd74&pin=ghgWGEfjyRGH64785&source=************&destination=************&ext=101&callerid=************';
                    // } else
                    //     $url = 'https://pbx.voxbaysolutions.com/api/clicktocall.php?uid=' . $ivr_number->uid . '&pin=' . $ivr_number->pin . '&source=' . $request->user()->vchr_user_mobile . '&destination=' . ($request->phone_number ?? $enquiry->vchr_customer_mobile) . '&ext=' . $ivr_number->extension . '&callerid=' . $ivr_number->vchr_virtual_number;
                }// $url='https://pbx.voxbaysolutions.com/api/clicktocall.php?uid=HJBKNgttd74&pin=ghgWGEfjyRGH64785&source=************&destination=************&ext=101&callerid=************';//.$ivr_number->vchr_virtual_number;
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_POST, false);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                $result = curl_exec($ch);
                curl_close($ch);
                #Echo Result Of FireBase Server
                if (trim($result) == 'success') {
                    $transfer_log = new \App\BackendModel\IvrTransferLog;
                    $transfer_log->user_id = $user_id;
                    $transfer_log->virtual_number = $ivr_number->vchr_virtual_number;
                    $transfer_log->agent_number = str_replace("+", "", $request->user()->vchr_user_mobile);
                    $transfer_log->customer_number = str_replace("+", "", $enquiry->vchr_customer_mobile);
                    $transfer_log->customer_name = $enquiry->vchr_customer_name;
                    $transfer_log->status = 0;
                    if ($request->expire_in && $request->expire_in != '')
                        $transfer_log->expire_at = Carbon::now()->addMinutes($request->expire_in)->format('Y-m-d- H:i:s');
                    $transfer_log->save();
                    $transfer_log->track_id = 'GLITR000' . $transfer_log->id;
                    $transfer_log->save();

                    $data['phone_number'] = $ivr_number->vchr_virtual_number;
                } else
                    return $this->response(200, true, $result);
            } else {
                // \Log::info('IVR number not exist:' . $user_id);
                return $this->response(200, true, "IVR number not exist");
            }
        } else {
            return $this->response(200, true, "Enquiry not exist");
        }
        return $this->response(200, false, null, $data);
    }

    public function reasonsList(Request $request)
    {
        if (Cache::get('reason_list' . $this->vendorId)) {
            $data = Cache::get('reason_list' . $this->vendorId);
        } else {
            $data = Reason::where('vendor_id', $this->vendorId)->select('id', 'name')->get();
            Cache::put('reason_list' . $this->vendorId, $data, null, true);
        }

        return $this->response(200, false, null, $data);
    }

    public function CallStatusList(Request $request)
    {
        $callstatus_list = CallStatus::where('vendor_id', $this->vendorId)
            ->select('id', 'name')->get();
        return $this->response(200, false, null, $callstatus_list);
    }

    public function summery_report(Request $request)
    {
        $from_date = Carbon::createFromFormat('m/d/Y', $request->from_date)->toDateString();
        $to_date = Carbon::createFromFormat('m/d/Y', $request->to_date)->toDateString();

        $agents = User::where('parent_user_id', $this->vendorId)
            ->select('pk_int_user_id as id', 'vchr_user_name as agent_name')->get();

        $data = [];
        $head = ["Agent Name", "Total"];
        $total_leads = 0;

        foreach ($agents as $agent) {
            $enquiryQa = Enquiry::where('staff_id', $agent->id)->where(function ($where) use ($from_date, $to_date) {
                $where->whereBetween('assigned_date', [Carbon::parse($from_date)->format('Y-m-d'), Carbon::parse($to_date)->format('Y-m-d')])
                    ->orWhere(function ($wh) use ($from_date, $to_date) {
                        $wh->whereNull('assigned_date')
                            ->whereBetween('created_at', [Carbon::parse($from_date)->format('Y-m-d') . ' 00:00:00', Carbon::parse($to_date)->format('Y-m-d') . ' 23:59:59']);
                    });
            })->get();

            $agent->total_count = $enquiryQa->count();
            $total_leads = $total_leads + $agent->total_count;

            $dd = [];
            $enquiryQ = EnquiryFollowup::whereHas('enquiry', function ($query) use ($agent) {
                $query->where('staff_id', $agent->id);
            })->where('log_type', EnquiryFollowup::TYPE_STATUS)
                ->whereBetween('created_at', [Carbon::parse($from_date)->format('Y-m-d') . ' 00:00:00', Carbon::parse($to_date)->format('Y-m-d') . ' 23:59:59'])
                ->get();

            $tStCount = 0;
            $all_status = FeedbackStatus::where('fk_int_user_id', $this->vendorId)->select('pk_int_feedback_status_id as id', 'vchr_status as status')->get();

            foreach ($all_status as $index => $a_status) {
                $enqCount = $enquiryQa
                    ->where('updated_at', '>=', Carbon::parse($from_date)->format('Y-m-d') . ' 00:00:00')
                    ->where('updated_at', '<=', Carbon::parse($to_date)->format('Y-m-d') . ' 23:59:59')
                    ->where('feedback_status', $a_status->id)->count();
                //$enqCount=$enquiryQ->where('note', $a_status->id)->count();
                $data['status'] = $a_status->status;
                $data['id'] = $a_status->id;
                $data['count'] = $enqCount;
                $tStCount = $tStCount + $enqCount;

                array_push($dd, $data);
                if (!in_array($a_status->status, $head)) {
                    array_push($head, $a_status->status);
                }
            }

            $agent->data = $dd;

        }
        // $response['heads'] = $head;
        $response['agents'] = $agents;
        return $this->response(200, false, null, $response);
    }

    public function enquiry_check_in_out(Request $request)
    {
        $validate_fields = [
            'enquiry_id' => ['required'],
            'log_status' => ['required'],
            'latitude' => ['required'],
            'longitude' => ['required']
        ];
        $validate_messages = [
            'enquiry_id.required' => 'Enquiry id is required',
            'log_status.required' => 'Status is required',
            'latitude.required' => 'Latitude is required',
            'longitude.required' => 'Longitude is required',
        ];
        $validated = $this->requestValidate($request, $validate_fields, $validate_messages);
        if ($validated) {
            return $validated;
        }

        DB::beginTransaction();
        try {
            $enq = Enquiry::where('pk_int_enquiry_id', $request->enquiry_id)->first();
            if ($enq->latitude == null && $enq->longitude == null) {
                $enq->latitude = $request->latitude;
                $enq->longitude = $request->longitude;
                $enq->save();
            }

            $loc = new EnquiryLocation();
            $loc->fk_int_enquiry_id = $request->enquiry_id;
            $loc->note = $request->note;
            $loc->note_id = $request->note_id;
            // $loc->poster_pasted=$request->poster_pasted;
            // $loc->products_properly_displayed=$request->products_properly_displayed;
            $loc->latitude = $request->latitude;
            $loc->longitude = $request->longitude;
            $loc->log_type = $request->log_status;
            $loc->user_id = $this->userId;
            $loc->save();
            $enquiryfollowup = new EnquiryFollowup();
            $enquiryfollowup->note = ($request->note) ? $request->note : 'check in';
            $enquiryfollowup->log_type = ($request->log_status == 1) ? EnquiryFollowup::TYPE_CHECKIN : EnquiryFollowup::TYPE_CHECKOUT;
            $enquiryfollowup->enquiry_id = $request->enquiry_id;
            $enquiryfollowup->bcc = $loc->id;
            $enquiryfollowup->created_by = $this->userId;
            $enquiryfollowup->save();

            $check_in = EnquiryFollowup::where('enquiry_id', $request->enquiry_id)->where('created_by', $this->userId)->where('log_type', EnquiryFollowup::TYPE_CHECKIN)->latest()->first();
            if ($check_in && $request->log_status != 1) {
                $check_in->checkout_id = $enquiryfollowup->id;
                $check_in->save();
            }

            //
            DB::commit();
            return $this->response(200, false, 'success', null);
        } catch (\Exception $exception) {
            DB::rollBack();
            \Log::info("Error Occurred While Updating (done) Log Status through API:" . $exception->getMessage());
            return $this->response(200, true, $exception->getMessage(), null);
        }

    }

    public function Checkout_noteList(Request $request)
    {
        $checkout_note_list = Checkout_note::query()
            ->where('vendor_id', $this->vendorId)
            ->select('id', 'name')
            ->get();
        return $this->response(200, false, null, $checkout_note_list);
    }

    /**
     * Get Lead Name
     * <AUTHOR>
     */

    public function getLeadName()
    {
        $enquiry = Enquiry::where('fk_int_user_id', $this->vendorId)
            ->where('vchr_customer_mobile', request()->mobile)
            ->select('pk_int_enquiry_id', 'vchr_customer_name')
            ->get();
        return $this->response(200, false, null, $checkout_note_list);
    }

    public function activities(Request $request)
    {
        Log::info('Activities endpoint is being called with params', [
            'request' => $request->all()
        ]);

        if ($request->date) {
            $filter_from = $request->date;
            $filter_to = $request->date;
        } elseif ($request->date_from) {
            $filter_from = $request->date_from;
            $filter_to = ($request->date_to) ? $request->date_to : $request->date_from;
        } else {
            $filter_from = Carbon::today()->format('Y-m-d');
            $filter_to = Carbon::today()->format('Y-m-d');
        }

        $i = 0;
        $da = [];

        if (Auth::user()->int_role_id == Variables::STAFF && Auth::user()->is_co_admin == 0) {
            $assignedStafId = AgentStaff::where('agent_id', Auth::user()->pk_int_user_id)
                ->pluck('staff_id')
                ->toArray();
            $authors = User::where(function ($where) use ($assignedStafId) {
                $where->where('pk_int_user_id', Auth::user()->pk_int_user_id)->orWhereIn('pk_int_user_id', $assignedStafId);
            })->get();
        } else {
            $authors = User::StaffCheck()
                ->whereNotIn('pk_int_user_id', ['3642', '4015'])
                ->where(function ($where) {
                    $where->where(function ($wh) {
                        $wh->where('int_role_id', User::STAFF)
                            ->where('parent_user_id', $this->vendorId);
                    })->orWhere('pk_int_user_id', Auth::id());
                })
                ->get();
        }

        $author_ids = $authors->pluck('pk_int_user_id')->toArray();
        $assignedStafId = [];
        if ($request->user()->int_role_id == User::STAFF && $request->user()->is_co_admin == 0) {
            $assignedStafId = AgentStaff::where('agent_id', $request->user()->pk_int_user_id)->pluck('staff_id')->toArray();
        }

        if (($request->type == 'lead' || $request->type == 'all') || !$request->type) {
            /* ------------  Enquiry data fetch --------------- */
            $enquiries = Enquiry::leftJoin('tbl_enquiry_types', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'tbl_enquiry_types.pk_int_enquiry_type_id')
                ->join('tbl_users', 'tbl_enquiries.created_by', '=', 'tbl_users.pk_int_user_id')
                ->where('tbl_enquiries.fk_int_user_id', $this->vendorId)
                ->whereBetween('tbl_enquiries.created_at', [$filter_from . ' 00:00:00', $filter_to . ' 23:59:59'])
                ->when($request->start_time, function ($query) use ($request) {
                    $query->whereTime('tbl_enquiries.created_at', '>=', $request->start_time);
                })
                ->when($request->end_time, function ($query) use ($request) {
                    $query->whereTime('tbl_enquiries.created_at', '<=', $request->end_time);
                })
                ->when($request->author_id && $request->author_id > 0, function ($query) use ($request) {
                    $query->where('tbl_enquiries.created_by', $request->author_id);
                })
                ->when($request->author_id == -1, function ($query) {
                    $query->whereNull('tbl_enquiries.created_by');
                })
                ->when($request->staff_id, function ($query) {
                    $query->whereNull('tbl_enquiries.created_by');
                })
                ->when(Auth::user()->int_role_id == User::STAFF && Auth::user()->is_co_admin == 0, function ($query) use ($assignedStafId) {
                    $query->where(function ($subQuery) use ($assignedStafId) {
                        $subQuery->where('tbl_enquiries.created_by', Auth::user()->pk_int_user_id)
                            ->orWhereIn('tbl_enquiries.created_by', $assignedStafId);
                    });
                })
                ->select([
                    'tbl_enquiry_types.vchr_enquiry_type',
                    'tbl_enquiries.vchr_customer_name',
                    'tbl_enquiries.vchr_customer_company_name',
                    'tbl_enquiries.mobile_no',
                    'tbl_enquiries.vchr_customer_mobile',
                    'tbl_enquiries.created_at',
                    'tbl_enquiries.pk_int_enquiry_id',
                    'tbl_enquiries.created_by',
                    'tbl_users.vchr_user_name'
                ])
                ->orderBy('tbl_enquiries.pk_int_enquiry_id', 'DESC')
                ->get();

            if ($request->activity_filters) {
                if (in_array("21", json_decode(request('activity_filters'))) || in_array("all", json_decode(request('activity_filters')))) {
                    $enquiries = $enquiries;
                } else {
                    $enquiries = [];
                }
            }

            foreach ($enquiries as $enquiry) {
                $da[$i]['time'] = $enquiry->created_at->diffForHumans();
                $da[$i]['date'] = $enquiry->created_at->format('Y-m-d H:i');
                $da[$i]['activity'] = 'added by';
                $da[$i]['agent'] = $enquiry->vchr_user_name;
                $da[$i]['added_to'] = $enquiry->vchr_enquiry_type;
                $da[$i]['activity_comment'] = 'New Enquiry';
                $da[$i]['activity_comment_2'] = 'via';
                $da[$i]['activity_comment_3'] = '';
                $da[$i]['activity_comment_4'] = '';
                $da[$i]['added_to'] = $enquiry->vchr_enquiry_type;
                $da[$i]['number'] = $enquiry->vchr_customer_mobile ?? '';
                $da[$i]['name'] = $enquiry->vchr_customer_name ?? '';
                $da[$i]['note'] = $enquiry->vchr_enquiry_type;
                $da[$i]['log_type'] = EnquiryFollowup::TYPE_NEW_LEAD;
                $i++;
            }

            /* ------------ Lead folowup activity --------------- */
            $activities = EnquiryFollowup::with([
                'task:id,name',  // Adding 'enquiry_followup_id' to avoid loading unnecessary fields
                'task.campaign:id,name',
                'enquiry:pk_int_enquiry_id,vchr_customer_name,vchr_customer_mobile',
                'source:pk_int_enquiry_type_id,vchr_enquiry_type',
                'purpose:pk_int_purpose_id,vchr_purpose'
                // Load only the necessary fields from the 'campaign' relationship
            ])->where('tbl_enquiries.fk_int_user_id', $this->vendorId)
                ->leftJoin('tbl_users as agent', 'agent.pk_int_user_id', '=', 'assigned_to')
                ->leftJoin('tbl_users as creator', 'creator.pk_int_user_id', '=', 'created_by')
                ->leftJoin('tbl_feedback_status as current_status', 'current_status.pk_int_feedback_status_id', '=', 'note')
                ->leftJoin('tbl_feedback_status as old_status', 'old_status.pk_int_feedback_status_id', '=', 'old_status_id')
                ->join('tbl_enquiries', 'enquiry_id', '=', 'pk_int_enquiry_id')
                ->whereBetween('tbl_enquiry_followups.created_at', [$filter_from . ' 00:00:00', $filter_to . ' 23:59:59'])
                ->where(function ($where) use ($author_ids, $request) {
                    if ($request->start_time)
                        $where->whereTime('tbl_enquiry_followups.created_at', '>=', $request->start_time);
                    if ($request->end_time)
                        $where->whereTime('tbl_enquiry_followups.created_at', '<=', $request->end_time);
                    if ($request->author_id && $request->author_id > 0)
                        $where->where('tbl_enquiry_followups.created_by', $request->author_id);
                    elseif ($request->author_id == -1)
                        $where->whereNull('tbl_enquiry_followups.created_by');
                    else
                        $where->whereIn('tbl_enquiry_followups.created_by', $author_ids)/* ->orWhereNull('tbl_enquiry_followups.created_by') */
                        ;
                })
                ->where(function ($q) use ($request) {
                    if ($request->staff_id)
                        $q->whereIn('tbl_enquiry_followups.created_by', json_decode(request('staff_id')));

                    if ($request->activity_filters)
                        if (!in_array("all", json_decode(request('activity_filters'))))
                            $q->whereIn('tbl_enquiry_followups.log_type', json_decode(request('activity_filters')));
                })
                ->select(
                    'tbl_enquiry_followups.*',
                    'tbl_enquiries.vchr_customer_name',
                    'vchr_customer_mobile',
                    'tbl_enquiries.pk_int_enquiry_id',
                    'agent.vchr_user_name as assigned_user',
                    'creator.vchr_user_name as assigned_by_user',
                    'current_status.vchr_status as c_status',
                    'old_status.vchr_status as o_status'
                )
                ->where(function ($where) use ($assignedStafId) {
                    if (Auth::user()->int_role_id == User::STAFF && Auth::user()->is_co_admin == 0)
                        $where->where('tbl_enquiry_followups.created_by', Auth::user()->pk_int_user_id)->orWhereIn('tbl_enquiry_followups.created_by', $assignedStafId);
                })
                ->orderBy('tbl_enquiry_followups.created_at', 'desc')
                ->get();
            $new_lead = array();

            foreach ($activities as $activity) {
                $da[$i]['time'] = $activity->created_at->diffForHumans();
                $da[$i]['date'] = $activity->created_at->format('Y-m-d H:i');
                if ($activity->log_type == EnquiryFollowup::TYPE_NEW) {
                    $new_lead[] = $activity->enquiry_id;
                    $da[$i]['activity'] = 'added by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = $activity->source ? $activity->source->vchr_enquiry_type : '';
                    $da[$i]['activity_comment'] = 'New Enquiry';
                    $da[$i]['activity_comment_2'] = 'via';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->enquiry->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->enquiry->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->source ? $activity->source->vchr_enquiry_type : '';
                    $da[$i]['log_type'] = EnquiryFollowup::TYPE_NEW_LEAD;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_NOTE) {
                    $da[$i]['activity'] = 'added By';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Note';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_LOG_CALL) {
                    $da[$i]['activity'] = 'by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Logged a Call';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_LOG_EMAIL) {
                    $da[$i]['activity'] = 'Send by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Email';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_LOG_MEETING) {
                    $da[$i]['activity'] = 'by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Logged a meeting';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_TASK) {
                    if ($activity->assigned_to) {
                        $da[$i]['activity'] = 'added by';
                        $da[$i]['activity_comment'] = 'had new task ';
                        $da[$i]['activity_comment_2'] = '';
                        $da[$i]['activity_comment_3'] = '';
                        $da[$i]['activity_comment_4'] = '';
                        $da[$i]['added_to'] = $activity?->assigned_user ?? 'Getlead';
                    } else {
                        $da[$i]['activity'] = 'created by';
                        $da[$i]['activity_comment'] = 'New  task';
                        $da[$i]['activity_comment_2'] = '';
                        $da[$i]['activity_comment_3'] = '';
                        $da[$i]['activity_comment_4'] = '';
                        $da[$i]['added_to'] = '';
                    }
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_SCHEDULE) {
                    $da[$i]['activity'] = 'created by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'New schedule ';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_STATUS) {
                    $activity->old = $activity?->o_status . ' to ' ?? '';
                    $activity->note = $activity?->c_status ?? 'Deleted Status';
                    $da[$i]['activity'] = 'changed by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Enquiry status';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ? $activity->old . $activity->note : '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_EMAIL) {
                    $da[$i]['activity'] = 'added by ';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Note ';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::WHATSAPP_HISTORY) {
                    $da[$i]['activity'] = 'added by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Note';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::SMS_HISTORY) {
                    $da[$i]['activity'] = 'by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Message sent';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::IVR) {
                    $da[$i]['activity'] = 'Added by ';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Calls';
                    $da[$i]['activity_comment_2'] = 'Through';
                    $da[$i]['activity_comment_3'] = 'IVR';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    if ($activity->response == 2) {
                        $da[$i]['note'] = (($activity->callMaster) ? $activity->callMaster->recording_url : '');
                    } else {
                        $da[$i]['note'] = 'https://pbx.voxbaysolutions.com/callrecordings/' . (($activity->ivrdata) ? str_replace(" ", "+", $activity->ivrdata->recording_url) : '');
                    }
                    $da[$i]['log_type'] = $activity->log_type;
                    $da[$i]['ivr_voice_url'] = $da[$i]['note'];

                } elseif ($activity->log_type == EnquiryFollowup::ENQ_PURPOSE) {
                    $da[$i]['activity'] = ' changed by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Purpose ';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->purpose ? $activity->purpose->vchr_purpose : '';
                    //  $da[$i]['note']= (EnquiryPurpose::where('pk_int_purpose_id', $activity->note)->first()) ? EnquiryPurpose::where('pk_int_purpose_id', $activity->note)->first()->vchr_purpose : '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_VOICE_NOTE) {
                    $da[$i]['activity'] = 'added by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Voice Note';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = FileUpload::viewFile($activity->note, 's3');
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_FILE_NOTE) {
                    $da[$i]['activity'] = 'added by ';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'File Note ';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = FileUpload::viewFile($activity->note, 's3');
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_DOCUMENT) {
                    $da[$i]['activity'] = 'added by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Document Note ';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = FileUpload::viewFile($activity->note, 's3');
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_ACTIVITY) {
                    $da[$i]['activity'] = 'changed by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Lead details ';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_CHECKIN) {
                    $loc = EnquiryLocation::where('id', $activity->bcc)->first();
                    $da[$i]['activity'] = 'by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Check-in ';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile;
                    $da[$i]['name'] = $activity->vchr_customer_name;
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                    $da[$i]['lat'] = ($loc) ? $loc->latitude : '';
                    $da[$i]['long'] = ($loc) ? $loc->longitude : '';
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_CHECKOUT) {
                    $note = Checkout_note::where('id', $activity->note)->first();
                    $loc = EnquiryLocation::where('id', $activity->bcc)->first();
                    $da[$i]['activity'] = 'by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'Check-out ';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile;
                    $da[$i]['name'] = $activity->vchr_customer_name;
                    $da[$i]['note'] = $note ? $note->name : '';
                    $da[$i]['log_type'] = $activity->log_type;
                    $da[$i]['lat'] = ($loc) ? $loc->latitude : '';
                    $da[$i]['long'] = ($loc) ? $loc->longitude : '';
                } elseif ($activity->log_type == EnquiryFollowup::TYPE_SCHEDULE) {
                    $da[$i]['activity'] = 'created by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'new  schedule';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->note ?? '';
                    $da[$i]['log_type'] = $activity->log_type;
                } elseif ($activity->log_type == EnquiryFollowup::TASK_COMPLETED) {
                    $da[$i]['activity'] = 'by';
                    $da[$i]['agent'] = $activity?->assigned_by_user ?? 'Getlead';
                    $da[$i]['added_to'] = '';
                    $da[$i]['activity_comment'] = 'completed task';
                    $da[$i]['activity_comment_2'] = '';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = $activity->vchr_customer_mobile ?? '';
                    $da[$i]['name'] = $activity->vchr_customer_name ?? '';
                    $da[$i]['note'] = $activity->task ? ($activity->task->campaign ? $activity->task->campaign->name : $activity->task->name) : '';
                    $da[$i]['log_type'] = $activity->log_type;
                }
                $i++;
            }

            /* ------------  Enquiry data fetch --------------- */


            $enquiries = Enquiry::leftJoin('tbl_enquiry_types', 'tbl_enquiries.fk_int_enquiry_type_id', '=', 'tbl_enquiry_types.pk_int_enquiry_type_id')
                ->join('tbl_users', 'tbl_enquiries.created_by', '=', 'tbl_users.pk_int_user_id')
                ->where('tbl_enquiries.fk_int_user_id', $this->vendorId)
                ->whereBetween('tbl_enquiries.created_at', [$filter_from . ' 00:00:00', $filter_to . ' 23:59:59'])
                ->when($request->start_time, function ($query) use ($request) {
                    $query->whereTime('tbl_enquiries.created_at', '>=', $request->start_time);
                })
                ->when($request->end_time, function ($query) use ($request) {
                    $query->whereTime('tbl_enquiries.created_at', '<=', $request->end_time);
                })
                ->when($request->author_id && $request->author_id > 0, function ($query) use ($request) {
                    $query->where('tbl_enquiries.created_by', $request->author_id);
                })
                ->when($request->author_id == -1, function ($query) {
                    $query->whereNull('tbl_enquiries.created_by');
                })
                ->when($request->staff_id, function ($query) {
                    $query->whereNull('tbl_enquiries.created_by');
                })
                ->when(Auth::user()->int_role_id == User::STAFF && Auth::user()->is_co_admin == 0, function ($query) use ($assignedStafId) {
                    $query->where(function ($subQuery) use ($assignedStafId) {
                        $subQuery->where('tbl_enquiries.created_by', Auth::user()->pk_int_user_id)
                            ->orWhereIn('tbl_enquiries.created_by', $assignedStafId);
                    });
                })
                ->select([
                    'tbl_enquiry_types.vchr_enquiry_type',
                    'tbl_enquiries.vchr_customer_name',
                    'tbl_enquiries.vchr_customer_company_name',
                    'tbl_enquiries.mobile_no',
                    'tbl_enquiries.vchr_customer_mobile',
                    'tbl_enquiries.created_at',
                    'tbl_enquiries.pk_int_enquiry_id',
                    'tbl_enquiries.created_by',
                    'tbl_users.vchr_user_name'
                ])
                ->orderBy('tbl_enquiries.pk_int_enquiry_id', 'DESC')
                ->whereNotIn('pk_int_enquiry_id', $new_lead)
                ->get();

            if ($request->activity_filters) {
                if (in_array("21", json_decode(request('activity_filters')))) {
                    $enquiries = $enquiries;
                } elseif (in_array("all", json_decode(request('activity_filters')))) {
                    $enquiries = $enquiries;
                } else {
                    $enquiries = [];
                }
            }

            foreach ($enquiries as $enquiry) {
                $da[$i]['time'] = $enquiry->created_at->diffForHumans();
                $da[$i]['date'] = $enquiry->created_at->format('Y-m-d H:i');
                $da[$i]['activity'] = 'added by';
                $da[$i]['agent'] = $enquiry->vchr_user_name;
                $da[$i]['added_to'] = $enquiry->vchr_enquiry_type;
                $da[$i]['activity_comment'] = 'New Enquiry';
                $da[$i]['activity_comment_2'] = 'via';
                $da[$i]['activity_comment_3'] = '';
                $da[$i]['activity_comment_4'] = '';
                $da[$i]['added_to'] = $enquiry->vchr_enquiry_type;
                $da[$i]['number'] = $enquiry->vchr_customer_mobile ?? '';
                $da[$i]['name'] = $enquiry->vchr_customer_name ?? '';
                $da[$i]['note'] = $enquiry->vchr_enquiry_type;
                $da[$i]['log_type'] = EnquiryFollowup::TYPE_NEW_LEAD;
                $i++;
            }


        }

        if (($request->type == 'deal' || $request->type == 'all') || !$request->type) {
            /* ------------ Deal created activity --------------- */
            $deals = Deal::where('vendor_id', $this->vendorId)
                ->leftJoin('tbl_users as agent', 'agent.pk_int_user_id', '=', 'agent_id')
                ->leftJoin('tbl_users as creator', 'creator.pk_int_user_id', '=', 'created_by')
                ->leftJoin('tbl_enquiries as tb', 'tb.pk_int_enquiry_id', '=', 'lead_id')
                ->whereBetween('deals.created_at', [$filter_from . ' 00:00:00', $filter_to . ' 23:59:59'])
                ->where(function ($where) use ($request) {
                    if ($request->staff_id)
                        $where->whereIn('agent_id', json_decode(request('staff_id')));
                })
                ->select('pk_int_deal_id', 'deal_name', 'lead_id', 'deals.updated_at', 'deals.agent_id', 'deals.created_at', 'vendor_id', 'vchr_customer_name', 'agent.vchr_user_name as assigned_user', 'creator.vchr_user_name as assigned_by_user')
                ->get();

            if ($request->activity_filters) {
                if (in_array("25", json_decode(request('activity_filters'))) || in_array("all", json_decode(request('activity_filters')))) {
                    $deals = $deals;
                } else {
                    $deals = [];
                }
            }

            foreach ($deals as $key => $deal) {
                $da[$i]['time'] = $deal->created_at->diffForHumans();
                $da[$i]['date'] = $deal->created_at->format('Y-m-d H:i');
                $da[$i]['activity'] = ' Created By ';
                $da[$i]['agent'] = $deal->assigned_user ?? '';
                $da[$i]['added_to'] = $deal->assigned_by_user ?? '';
                $da[$i]['activity_comment'] = 'Deal';
                $da[$i]['activity_comment_2'] = 'To';
                $da[$i]['activity_comment_3'] = '';
                $da[$i]['activity_comment_4'] = '';
                $da[$i]['number'] = '';
                $da[$i]['name'] = $deal?->vchr_customer_name ?? '';
                $da[$i]['note'] = $deal->deal_name ?? '';
                $da[$i]['log_type'] = (string)EnquiryFollowup::TYPE_DEAL;
                $i++;
            }

            /* ------------ Deal activity --------------- */
            $deal_activity = DealActivity::leftJoin('tbl_users as agent', 'agent.pk_int_user_id', '=', 'note')
                ->leftJoin('tbl_users as creator', 'creator.pk_int_user_id', '=', 'created_by')
                ->leftJoin('meeting_outcomes', 'meeting_outcomes.id', '=', 'deal_activities.outcome')
                ->leftJoin('call_status', 'call_status.id', '=', 'deal_activities.outcome')
                ->whereBetween('deal_activities.created_at', [$filter_from . ' 00:00:00', $filter_to . ' 23:59:59'])
                ->where(function ($where) use ($author_ids, $request) {
                    if ($request->start_time)
                        $where->whereTime('deal_activities.created_at', '>=', $request->start_time);
                    if ($request->end_time)
                        $where->whereTime('deal_activities.created_at', '<=', $request->end_time);
                    if ($request->activity_filters)
                        if (!in_array("all", json_decode(request('activity_filters'))))
                            $where->whereIn('deal_activities.log_type', json_decode(request('activity_filters')));
                })
                ->when($request->staff_id, function ($q) {
                    return $q->whereIn('deal_activities.created_by', json_decode(request('staff_id')));
                })
                ->when(!$request->staff_id, function ($q) use ($request) {
                    return $q->where('deal_activities.created_by', $request->user()->pk_int_user_id);
                })
                ->select('deal_activities.*', 'meeting_outcomes.name as meeting_outcome', 'call_status.name as call_status', 'agent.vchr_user_name as assigned_user', 'creator.vchr_user_name as assigned_by_user')
                ->orderBy('deal_activities.created_at', 'desc')
                ->get();

            foreach ($deal_activity as $act_deal) {
                $activity = '';
                $log_type = '';
                if ($act_deal->log_type == DealActivity::TYPE_NOTE) {
                    $activity = 'Added By';
                    $log_type = '2';
                } elseif ($act_deal->log_type == DealActivity::TYPE_STAGE) {
                    $activity = 'Changed By';
                    $log_type = '3';
                } elseif ($act_deal->log_type == DealActivity::TYPE_DEAL_TYPE) {
                    $activity = 'Changed By';
                    $log_type = '4';
                } elseif ($act_deal->log_type == DealActivity::TYPE_AGENT) {
                    $activity = 'Changed By';
                    $log_type = '5';
                } elseif ($act_deal->log_type == DealActivity::TYPE_DATE) {
                    $activity = 'Changed By';
                    $log_type = '7';
                } elseif ($act_deal->log_type == DealActivity::TYPE_DETAILS) {
                    $activity = 'Note Lefted By';
                    $log_type = '12';
                } elseif ($act_deal->log_type == DealActivity::TYPE_VOICE_NOTE) {
                    $activity = 'Added By';
                    $log_type = '13';
                } elseif ($act_deal->log_type == DealActivity::TYPE_FILE_NOTE) {
                    $activity = 'Added By';
                    $log_type = '14';
                } elseif ($act_deal->log_type == DealActivity::TYPE_DOCUMENT) {
                    $activity = 'Added By';
                    $log_type = '15';
                } elseif ($act_deal->log_type == DealActivity::TYPE_LOG_CALL) {
                    $activity = 'Added By';
                    $log_type = '17';
                } elseif ($act_deal->log_type == DealActivity::TYPE_LOG_EMAIL) {
                    $activity = 'Note Lefted By';
                    $log_type = '18';
                } elseif ($act_deal->log_type == DealActivity::TYPE_LOG_MEETING) {
                    $activity = 'Note Lefted By';
                    $log_type = '19';
                }

                if ($log_type != '') {
                    $da[$i]['time'] = Carbon::parse($act_deal->created_at)->diffForHumans();
                    $da[$i]['date'] = $act_deal->created_at;
                    $da[$i]['activity'] = $activity;
                    $da[$i]['agent'] = $act_deal?->assigned_user ?? '';
                    $da[$i]['added_to'] = $act_deal?->assigned_by_user ?? '';
                    $da[$i]['activity_comment'] = 'Deal';
                    $da[$i]['activity_comment_2'] = 'To';
                    $da[$i]['activity_comment_3'] = '';
                    $da[$i]['activity_comment_4'] = '';
                    $da[$i]['number'] = '';
                    $da[$i]['name'] = $act_deal->customer_name ?? '';
                    $da[$i]['note'] = $act_deal->deal_name ?? '';
                    $da[$i]['log_type'] = $log_type;
                    $i++;
                }
            }
            /* ------------ Deal task creation activity --------------- */
            $selectColumns = [
                'deal_tasks.id',
                'deal_tasks.name',
                'deal_tasks.scheduled_date',
                'deal_tasks.assigned_to',
                'deal_tasks.assigned_by',
                'deal_tasks.vendor_id',
                'deal_tasks.deal_id',
                'deals.lead_id as enquiry_id',
                'deal_tasks.status',
                'deal_tasks.created_at',
                'deals.deal_name as deal_name',
                'tbl_enquiries.vchr_customer_name as customer_name',
            ];

            $dealtask = DealTask::where('deal_tasks.vendor_id', $this->vendorId)
                ->leftJoin('tbl_users as agent', 'agent.pk_int_user_id', '=', 'assigned_to')
                ->leftJoin('tbl_users as creator', 'creator.pk_int_user_id', '=', 'assigned_by')
                ->leftjoin('deals', 'deal_id', '=', 'deals.pk_int_deal_id')
                ->leftjoin('tbl_enquiries', 'deals.lead_id', '=', 'tbl_enquiries.pk_int_enquiry_id')
                ->whereBetween('deal_tasks.created_at', [$filter_from . ' 00:00:00', $filter_to . ' 23:59:59'])
                ->when($request->staff_id, function ($q) {
                    return $q->whereIn('deal_tasks.assigned_by', json_decode(request('staff_id')));
                })
                ->select('id', 'name', 'deal_id', 'pk_int_enquiry_id', 'deal_tasks.created_at', 'assigned_to', 'assigned_by', 'status', 'deal_tasks.vendor_id', 'deal_tasks.updated_at', 'vchr_customer_name as customer_name', 'agent.vchr_user_name as assigned_user', 'creator.vchr_user_name as assigned_by_user')
                ->orderBy('deal_tasks.id', 'DESC');
            $dealtask = $dealtask->get();

            if ($request->activity_filters) {
                if (in_array("16", json_decode(request('activity_filters'))) || in_array("all", json_decode(request('activity_filters')))) {
                    $dealtask = $dealtask;
                } else {
                    $dealtask = [];
                }
            }

            foreach ($dealtask as $task_deal) {
                $da[$i]['time'] = Carbon::parse($task_deal->created_at)->diffForHumans();
                $da[$i]['date'] = $task_deal->created_at;
                $da[$i]['activity'] = ' Created By ';
                $da[$i]['agent'] = $task_deal?->assigned_by_user ?? '';
                $da[$i]['added_to'] = $task_deal?->assigned_user ?? '';
                $da[$i]['activity_comment'] = 'Task';
                $da[$i]['activity_comment_2'] = '';
                $da[$i]['activity_comment_3'] = '';
                $da[$i]['activity_comment_4'] = '';
                $da[$i]['number'] = '';
                $da[$i]['name'] = $task_deal->customer_name ?? '';
                $da[$i]['note'] = $task_deal->name ?? '';
                $da[$i]['log_type'] = '16';
                $i++;
            }

            /* ------------ Deal task history activity --------------- */
            $deal_task_history = DealTaskHistory::where('deal_task_histories.vendor_id', $this->vendorId)
                ->with(['creator:pk_int_user_id,vchr_user_name', 'task:deal_id,id', 'task.deal:pk_int_deal_id,deal_name'])
                ->whereBetween('deal_task_histories.created_at', [$filter_from . ' 00:00:00', $filter_to . ' 23:59:59'])
                ->where(function ($where) use ($author_ids, $request) {
                    if ($request->start_time)
                        $where->whereTime('deal_task_histories.created_at', '>=', $request->start_time);
                    if ($request->end_time)
                        $where->whereTime('deal_task_histories.created_at', '<=', $request->end_time);
                })
                ->leftjoin('deal_tasks', 'deal_task_id', '=', 'deal_tasks.id')
                ->select('deal_tasks.name as deal_task_name', 'deal_task_histories.*')
                ->when(request('author_id'), function ($q) {
                    return $q->where('deal_task_histories.updated_by', request('author_id'));
                })
                ->get();

            $getDealname = getDealName(true) . ' Task ';
            foreach ($deal_task_history as $d_history) {
                $da[$i]['time'] = Carbon::parse($d_history->created_at)->diffForHumans();
                $da[$i]['date'] = Carbon::parse($d_history->created_at)->format('Y-m-d H:i');
                $da[$i]['activity'] = 'Updated By';
                $da[$i]['agent'] = $d_history?->creator?->vchr_user_name ?? '';
                $da[$i]['added_to'] = '';
                $da[$i]['activity_comment'] = $getDealname;
                $da[$i]['activity_comment_2'] = '';
                $da[$i]['activity_comment_3'] = '';
                $da[$i]['activity_comment_4'] = '';
                $da[$i]['number'] = '';
                $da[$i]['name'] = $d_history?->task?->deal?->deal_name ?? '';
                $da[$i]['note'] = $d_history->task_status ?? '';
                $da[$i]['log_type'] = '23';
                $i++;
            }
        }

        if (($request->type == 'task' || $request->type == 'all') || !$request->type) {
            /* ------------ Lead task creation activity --------------- */
            $tasksQuery = Task::where('vendor_id', $this->vendorId)
                ->leftJoin('tbl_users as agent', 'agent.pk_int_user_id', '=', 'assigned_to')
                ->leftJoin('tbl_users as creator', 'creator.pk_int_user_id', '=', 'assigned_by')
                ->leftJoin('tbl_enquiries as tb', 'tb.pk_int_enquiry_id', '=', 'enquiry_id')
                ->whereBetween('tasks.created_at', [$filter_from . ' 00:00:00', $filter_to . ' 23:59:59'])
                ->select('id', 'name', 'enquiry_id', 'tasks.created_at', 'assigned_to', 'assigned_by', 'status', 'vendor_id', 'tasks.updated_at', 'vchr_customer_name', 'agent.vchr_user_name as assigned_user', 'creator.vchr_user_name as assigned_by_user');

            // Filter by author_id
            if ($request->author_id && $request->author_id > 0) {
                $tasksQuery->where('tasks.assigned_by', $request->author_id);
            } elseif ($request->author_id == -1) {
                $tasksQuery->whereNull('tasks.assigned_by');
            } else {
                $tasksQuery->whereIn('tasks.assigned_by', $author_ids);
            }

            // Filter by user role and co-admin status
            if ($request->user()->int_role_id == User::STAFF && $request->user()->is_co_admin == 0) {
                $tasksQuery->where('tasks.assigned_by', $request->user()->pk_int_user_id);
            }

            // Filter by staff_id
            if ($request->staff_id) {
                $tasksQuery->whereIn('tasks.assigned_to', json_decode($request->staff_id));
            }

            $tasks = $tasksQuery->get();

            if ($request->activity_filters)
                if (in_array("22", json_decode(request('activity_filters'))) || in_array("all", json_decode(request('activity_filters')))) {
                    $tasks = $tasks;
                } else {
                    $tasks = [];
                }

            foreach ($tasks as $task) {
                $da[$i]['time'] = Carbon::parse($task->created_at)->diffForHumans();
                $da[$i]['date'] = $task->created_at;
                $da[$i]['activity'] = ' Created By ';
                $da[$i]['agent'] = $task?->assigned_by_user ?? '';
                $da[$i]['added_to'] = $task?->assigned_user ?? '';
                $da[$i]['activity_comment'] = 'Task';
                $da[$i]['activity_comment_2'] = 'To';
                $da[$i]['activity_comment_3'] = '';
                $da[$i]['activity_comment_4'] = '';
                $da[$i]['number'] = '';
                $da[$i]['name'] = $task?->vchr_customer_name ?? 'No name';
                $da[$i]['note'] = $task->name ?? '';
                $da[$i]['log_type'] = '22';
                $i++;
            }

            /* ------------ Lead task history activity --------------- */
            $task_histories = TaskHistory::where('task_histories.vendor_id', $this->vendorId)
                ->leftJoin('tbl_users as creator', 'creator.pk_int_user_id', '=', 'updated_by')
                ->leftJoin('tasks as tb', 'tb.id', '=', 'task_histories.task_id')
                ->leftJoin('tbl_enquiries as te', 'te.pk_int_enquiry_id', '=', 'tb.enquiry_id')
                ->whereBetween('task_histories.created_at', [$filter_from . ' 00:00:00', $filter_to . ' 23:59:59'])
                ->where(function ($where) use ($author_ids, $request) {
                    $where->whereIn('task_histories.updated_by', $author_ids)->orWhereNull('task_histories.updated_by');
                })
                ->where(function ($where) use ($assignedStafId, $request) {
                    if ($request->user()->int_role_id == User::STAFF && $request->user()->is_co_admin == 0)
                        $where->where('task_histories.updated_by', $request->user()->pk_int_user_id)->orWhereIn('task_histories.updated_by', $assignedStafId);
                })
                ->where(function ($where) use ($request) {
                    if ($request->staff_id)
                        $where->whereIn('task_histories.updated_by', json_decode(request('staff_id')));
                })
                ->select('creator.vchr_user_name', 'te.vchr_customer_name', 'task_histories.task_status', 'task_histories.updated_by', 'task_histories.created_at', 'task_histories.vendor_id', 'task_histories.task_id')
                ->get();

            if ($request->activity_filters)
                if (in_array("23", json_decode(request('activity_filters'))) || in_array("all", json_decode(request('activity_filters')))) {
                    $task_histories = $task_histories;
                } else {
                    $task_histories = [];
                }

            foreach ($task_histories as $history) {
                $da[$i]['time'] = Carbon::parse($history->created_at)->diffForHumans();
                $da[$i]['date'] = Carbon::parse($history->created_at)->format('Y-m-d H:i');
                $da[$i]['activity'] = 'Updated By';
                $da[$i]['agent'] = $history?->vchr_user_name ?? '';
                $da[$i]['added_to'] = '';
                $da[$i]['activity_comment'] = 'Task ';
                $da[$i]['activity_comment_2'] = '';
                $da[$i]['activity_comment_3'] = '';
                $da[$i]['activity_comment_4'] = '';
                $da[$i]['number'] = '';
                $da[$i]['name'] = $history?->vchr_customer_name ?? '';
                $da[$i]['note'] = $history->task_status ?? '';
                $da[$i]['log_type'] = '23';
                $i++;
            }
        }

        if (($request->type == 'visitor' || $request->type == 'all') || !$request->type) {
            /* ------------ Visitor activity --------------- */
            $visits = VisitUser::where('visit_users.vendor_id', $this->vendorId)
                ->leftJoin('tbl_users as agent', 'agent.pk_int_user_id', '=', 'agent_id')
                ->leftJoin('tbl_users as creator', 'creator.pk_int_user_id', '=', 'created_by')
                ->leftJoin('tbl_enquiry_purpose as purpose', 'purpose.pk_int_purpose_id', '=', 'purpose_id')
                ->leftJoin('tbl_enquiries as tb', 'tb.pk_int_enquiry_id', '=', 'enquiry_id')
                ->whereBetween('visit_users.date_in', [$filter_from . ' 00:00:00', $filter_to . ' 23:59:59'])
                ->where(function ($where) use ($request) {
                    if ($request->staff_id)
                        $where->whereIn('agent_id', json_decode(request('staff_id')));
                })
                ->select('visit_users.vendor_id', 'visit_note', 'visit_users.date_in', 'agent_id', 'tb.vchr_customer_name', 'purpose.vchr_purpose', 'visit_users.created_at', 'agent.vchr_user_name as assigned_user', 'creator.vchr_user_name as assigned_by_user')
                ->get();
            foreach ($visits as $value) {
                $da[$i]['time'] = $value->created_at->diffForHumans();
                $da[$i]['date'] = Carbon::parse($value->date_in)->format('Y-m-d H:i');
                $da[$i]['activity'] = 'added by';
                $da[$i]['agent'] = $value?->assigned_user ?? '';
                $da[$i]['added_to'] = $value?->assigned_by_user ?? '';
                $da[$i]['activity_comment'] = 'Visit';
                $da[$i]['activity_comment_2'] = '';
                $da[$i]['activity_comment_3'] = '';
                $da[$i]['activity_comment_4'] = '';
                $da[$i]['number'] = '';
                $da[$i]['name'] = $value?->vchr_customer_name ?? '';
                $da[$i]['note'] = $value->visit_note ?? '';
                $da[$i]['log_type'] = EnquiryFollowup::TYPE_VISIT;
                $da[$i]['purpose'] = $value?->vchr_purpose ?? '';
                $i++;
            }
        }

        if ($request->activity_filters && in_array("22", json_decode(request('activity_filters')))) {
            $tasks = $tasks;
        } elseif ($request->activity_filters && in_array("all", json_decode(request('activity_filters')))) {
            $tasks = $tasks;
        }

        if ($request->activity_filters && in_array("23", json_decode(request('activity_filters')))) {
            $task_histories = $task_histories;
        } elseif ($request->activity_filters && in_array("all", json_decode(request('activity_filters')))) {
            $task_histories = $task_histories;
        }

        if ($request->activity_filters && in_array("25", json_decode(request('activity_filters')))) {
            $deals = $deals;
        } elseif ($request->activity_filters && in_array("all", json_decode(request('activity_filters')))) {
            $deals = $deals;
        }

        $result = collect($da)->sortByDesc('date')->values();
        return $this->response(200, false, null, $result);

    }

    public function lead_count_report(Request $request)
    {
        Log::info('lead analysis report', ['input' => $request->all()]);

        /**
         * @var User $user
         */
        $user = Auth::user();

        $enquiryStatusWiseCount = Enquiry::query()
            ->selectRaw('feedback_status,COUNT(*) as total_count')
            ->where('fk_int_user_id', $user->getBusinessId())
            ->when($request->has('staff_id'), function ($query) use ($request) {
                $query->whereIn('staff_id', (array)json_decode($request->input('staff_id'), true));
            })
            ->when($request->has('enquiry_source_id'), function ($query) use ($request) {
                $query->whereIn('fk_int_enquiry_type_id', (array)json_decode($request->input('fk_int_enquiry_type_id'), true));
            })
            ->where(function ($q) use ($request) {
                $request->date_filter == "month" ? $q->whereBetween('tbl_enquiries.created_at', [Carbon::now()->startOfMonth()->toDateString(), Carbon::now()->endOfMonth()->toDateString()]) : '';
                $request->date_filter == "today" ? $q->whereBetween('tbl_enquiries.created_at', [Carbon::today()->format('Y-m-d') . " 00:00:00", Carbon::today()->format('Y-m-d') . " 23:59:59"]) : '';
                $request->date_filter == "week" ? $q->whereBetween('tbl_enquiries.created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]) : '';
                $request->date_filter == "custom" && $request->date_from != null && $request->date_to != null ? $q->whereBetween('tbl_enquiries.created_at', [$request->date_from . " 00:00:00", $request->date_to . " 23:59:59"]) : '';
            })
            ->when(
                $user->isOpsStaff(),
                static fn($query) => $query
                    ->where(static fn($query) => $query->whereIn('staff_id', static function ($subQuery) use (
                        $user
                    ): void {
                        $subQuery->select('staff_id')
                            ->from('agent_staffs')
                            ->where('agent_id', $user->pk_int_user_id);
                    })->orWhere('staff_id', $user->pk_int_user_id))
            )
            ->groupBy('feedback_status')
            ->get()
            ->toBase()
            ->mapWithKeys(static function (Enquiry $enquiry) {
                return [$enquiry->feedback_status ?? "ytc" => $enquiry->getAttribute('total_count')];
            });

        Log::info('enquiry status wise count', ['counts' => $enquiryStatusWiseCount, 'user_id' => $user->pk_int_user_id]);

        $response = FeedbackStatus::query()
            ->select('pk_int_feedback_status_id', 'vchr_status')
            ->whereIn('pk_int_feedback_status_id', $enquiryStatusWiseCount->except("ytc")->keys())
            ->cursor()
            ->map(static function (FeedbackStatus $feedbackStatus) use ($enquiryStatusWiseCount) {
                return [
                    'id' => $feedbackStatus->pk_int_feedback_status_id,
                    'status' => $feedbackStatus->vchr_status,
                    'count' => $enquiryStatusWiseCount[$feedbackStatus->pk_int_feedback_status_id] ?? 0,
                ];
            })
            ->reject(static fn($feedbackStatus) => $feedbackStatus['count'] === 0)
            ->collect();

        if (($enquiryStatusWiseCount['ytc'] ?? 0) > 0) {
            $response->push([
                "id" => 0,
                "status" => "Yet to Contact",
                "count" => $enquiryStatusWiseCount['ytc']
            ]);
        }

        return $this->response(200, false, null,
            ['total_lead' => $enquiryStatusWiseCount->sum(),
                'status' => $response]
        );
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     * Deal Activity
     * <AUTHOR>
     */

    public function leadActivity()
    {
        $activities = EnquiryFollowup::where('enquiry_id', request('id'))
            ->whereHas('enquiry', function ($query) {
                if ((request()->has('from') && request('from') != "") && (request()->has('to') && request('to') != "")) {
                    $query->whereDate('created_at', '>=', request('from'))
                        ->whereDate('created_at', '<=', request('to'));
                }
                if (request('enquiry_purpose_id')) {
                    $query->whereIn('enquiry_purpose_id', json_decode(request('enquiry_purpose_id')));
                }
                if (request('enquiry_source_id')) {
                    $query->whereIn('enquiry_source_id', json_decode(request('enquiry_source_id')));
                }
                if (request('enquiry_status_id')) {
                    $query->whereIn('enquiry_status_id', json_decode(request('enquiry_status_id')));
                }
                if (request('lead_type_id')) {
                    $query->whereIn('lead_type_id', json_decode(request('lead_type_id')));
                }
                if (request('staff_id')) {
                    $query->whereIn('staff_id', json_decode(request('staff_id')));
                }
            })
            ->orderByRaw("id DESC, created_at DESC")
            ->get();

        $activities = $activities->map(function ($activity) {
            return $this->getLeadActivityTrigger($activity);
        });
        $enquiry = Enquiry::find(request('id'));
        if (!$enquiry)
            $enquiry = Enquiry::onlyTrashed()->find(request('id'));

        $source = EnquiryType::find($enquiry->fk_int_enquiry_type_id ?? null);
        $created_log = (object)[];
        $created_log->id = 0;
        $created_log->time = ($enquiry->created_at) ? Carbon::parse($enquiry->created_at)->diffForHumans() : '';
        $created_log->created_at = $enquiry->created_at->format('Y-m-d H:i');
        $created_log->created_by = $enquiry->createdBy ? $enquiry->createdBy->vchr_customer_name : '';
        $created_log->agent_name = $enquiry->assigned_user ? $enquiry->assigned_user->vchr_customer_name : '';
        $created_log->ivr_voice_url = null;
        $created_log->action = 'Created a';
        $created_log->activity = 'New Lead';
        $created_log->join_text = 'via';
        $created_log->content = ($source ? $source->vchr_enquiry_type : 'Website');
        $created_log->log_type = 0;
        $created_log->date = null;
        $created_log->task_id = null;
        $created_log->task_category = null;
        $created_log->task_status = null;
        $created_log->outcome = null;
        $activities->push($created_log);
        $deal = Deal::where('lead_id', request('id'))->get();
        $task = Task::NonCampaign()->where('enquiry_id', request('id'))->get();
        $visits = $enquiry->visit;
        if ($deal)
            foreach ($deal as $value) {
                $obj['id'] = $value->pk_int_deal_id;
                $obj['time'] = $value->created_at->diffForHumans();
                $obj['created_at'] = $value->created_at->format('Y-m-d H:i');
                $obj['created_by'] = $value->user ? $value->user->vchr_user_name : '';
                $obj['agent_name'] = $value->agent ? $value->agent->vchr_user_name : '';
                $obj['ivr_voice_url'] = null;
                $obj['action'] = 'added a';
                $obj['activity'] = 'New ' . getDealName(true);
                $obj['join_text'] = '';
                $obj['content'] = $value->deal_name ?? "";
                $obj['old_content'] = '';
                $obj['log_type'] = EnquiryFollowup::TYPE_DEAL;
                $obj['date'] = $value->created_at->format('Y-m-d H:i');
                $obj['task_id'] = $value->pk_int_deal_id;
                $obj['task_category'] = null;
                $obj['task_status'] = null;
                $obj['outcome'] = null;
                $activities->push($obj);
            }
        if ($task)
            foreach ($task as $value) {
                $obj2['id'] = $value->id;
                $obj2['time'] = $value->created_at->diffForHumans();
                $obj2['created_at'] = $value->created_at->format('Y-m-d H:i');
                $obj2['created_by'] = $value->added_by ? $value->added_by->vchr_user_name : '';
                $obj2['agent_name'] = $value->agent ? $value->agent->vchr_user_name : '';
                $obj2['ivr_voice_url'] = null;
                $obj2['action'] = 'added a';
                $obj2['activity'] = 'New task';
                $obj2['join_text'] = '';
                $obj2['content'] = $value->name ?? '';
                $obj2['old_content'] = '';
                $obj2['log_type'] = EnquiryFollowup::TYPE_TASK_NEW;
                $obj2['date'] = Carbon::parse($value->scheduled_date)->format('Y-m-d H:i');
                $obj2['task_id'] = $value->id;
                $obj2['task_category'] = $value->task_category_id ?? null;
                $obj2['task_status'] = $value->status ?? null;
                $obj2['outcome'] = null;

                $activities->push($obj2);
            }
        if ($visits)
            foreach ($visits as $value) {
                $obj2['id'] = $value->id;
                $obj2['time'] = $value->created_at->diffForHumans();
                $obj2['created_at'] = $value->created_at->format('Y-m-d H:i');
                $obj2['created_by'] = '';
                $obj2['agent_name'] = $value->agent->first() ? $value->agent->first()->vchr_user_name : '';
                $obj2['ivr_voice_url'] = null;
                $obj2['action'] = '';
                $obj2['activity'] = 'Customer visited';
                $obj2['join_text'] = '';
                $obj2['content'] = $value->visit_note ?? '';
                $obj2['old_content'] = '';
                $obj2['log_type'] = EnquiryFollowup::TYPE_VISIT;
                $obj2['date'] = Carbon::parse($value->date_in)->format('Y-m-d H:i');
                $obj2['task_id'] = $value->id;
                $obj2['task_category'] = null;
                $obj2['task_status'] = null;
                $obj2['outcome'] = null;
                $obj2['purpose'] = $value->purpose->first() ? $value->purpose->first()->vchr_purpose : '';

                $activities->push($obj2);
            }
        $activities = $activities->sortByDesc('created_at')->values();

        return $this->response(200, false, 'success', $activities);
    }

    public static function updateAdditionalField(Request $request)
    {
        $value = $request->additional_value;
        $index = $request->additional_key;
        $id = $request->enquiry_id;
        $fieldName = '';
        if ($value && $value != "" && $index && $id) {
            $field_name = LeadAdditionalField::find($index);
            $exists_field = LeadAdditionalDetails::where('field_id', $field_name->id)
                ->where('field_name', $field_name->field_name)
                ->where('enquiry_id', $id)
                ->first();
            $fieldName = $field_name->field_name;
            if ($exists_field) {
                if ($field_name->type_text == 'Image') {
                    $filename = $value->store('public/custom_field_image');
                    $exists_field->value = $filename;
                } elseif ($field_name->input_type == 8) {// Multi Select DropDown
                    $exists_field->value = json_encode($value);
                } else {
                    $exists_field->value = $value;
                }
                $exists_field->save();
            } else {
                $fieldName = $field_name->field_name;
                $additionalDetails = new LeadAdditionalDetails();
                $additionalDetails->enquiry_id = $id;
                $additionalDetails->field_id = $index;
                $additionalDetails->field_name = $field_name->field_name;

                if ($field_name->type_text == 'Image') {
                    $filename = $value->store('public/custom_field_image');
                    $additionalDetails->value = $filename;
                } elseif ($field_name->input_type == 8) {// Multi Select DropDown
                    $additionalDetails->value = json_encode($value);
                } else {
                    $additionalDetails->value = $value;
                }
                $additionalDetails->created_by = Auth::user()->pk_int_user_id;
                $additionalDetails->save();
            }

            // Additional field automation when update
            $enq = Enquiry::find($id);
            $assignAdditionalData = AutomationRule::where('trigger', 'value_change')
                ->where('vendor_id', $enq->fk_int_user_id)
                // ->where('enquiry_source_id',$enq->fk_int_enquiry_type_id)
                ->where('action', 'assign')
                ->orderby('id', 'DESC')
                ->get();

            if (count($assignAdditionalData) > 0) {
                foreach ($assignAdditionalData as $key => $value) {
                    $enqAddField = LeadAdditionalDetails::where('enquiry_id', $enq->pk_int_enquiry_id)
                        ->where('field_id', $value->additional_field)
                        ->where(function ($q) use ($value, $field_name) {
                            if ($field_name->input_type == 8) {
                                $q->whereJsonContains('value', $value->additional_field_value);
                            } elseif ($field_name->input_type == 2) {
                                $q->where('value', $value->additional_field_value);
                            }
                        })
                        ->first();

                    if ($enqAddField) {
                        $leadAdd = $enqAddField;
                    } else {
                        $assignAdditionalData->forget($key);
                    }

                }
            }

            $assignAdditionalData = $assignAdditionalData->first();

            if ($assignAdditionalData && $leadAdd) {
                $fieldd = LeadAdditionalField::find($assignAdditionalData->additional_field);
                if ($fieldd->input_type == 2) {
                    if ($leadAdd->field_id == $assignAdditionalData->additional_field && $leadAdd->value == $assignAdditionalData->additional_field_value) {
                        AutomationRule::autoassign($assignAdditionalData, $id);
                    }
                } elseif ($fieldd->input_type == 8) {
                    if ($leadAdd->field_id == $assignAdditionalData->additional_field) {
                        if (in_array($assignAdditionalData->additional_field_value, json_decode($leadAdd->value)))
                            AutomationRule::autoassign($assignAdditionalData, $id);
                    }
                }

            }
        }
        return $fieldName;
    }
}
